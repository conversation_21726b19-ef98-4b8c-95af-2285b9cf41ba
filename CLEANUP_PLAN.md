# 🧹 过时文件清理计划

## 📋 清理概述

基于系统当前状态和Gemini完全处理架构迁移，以下文件可以被安全清理：

## 🗑️ 推荐清理的文件

### 1. 调试和测试工具（已集成到监控系统）

#### A. 独立调试工具
- `js/responsible-person-debugger.js` - 详细调试器，使用频率低
- `js/responsible-person-test.js` - 测试脚本，功能已验证完成
- `js/runtime-button-test.js` - 运行时测试，已被监控系统替代
- `js/order-creation-debugger.js` - 订单创建调试器，重复功能
- `js/network-diagnostics.js` - 网络诊断，基础功能

#### B. 修复工具（功能已稳定）
- `js/comprehensive-button-fix.js` - 综合按钮修复器
- `js/responsible-person-fix.js` - 负责人字段修复器

### 2. 重复的测试页面

#### A. 旧版多订单测试
- `test-multi-order.html` - 被新的Gemini测试页面替代
- `test-multi-order-refactored.html` - 重构测试，功能已验证

#### B. 特定功能测试页面
- `test-responsible-person.html` - 负责人功能测试，已完成验证
- `test-dropdown-fix.html` - 下拉菜单修复测试，已完成

### 3. 过时的文档和验证文件

#### A. 完成状态报告
- `VERIFICATION_COMPLETE_REPORT.md` - 验证完成报告
- `REFACTOR_COMPLETION_SUMMARY.md` - 重构完成总结

## 💾 保留的核心文件

### 测试页面（保留）
- `test-gemini-multi-order.html` - 最新的Gemini测试页面
- `test-date-and-history.html` - 日期和历史功能测试
- `debug-order-creation.html` - 主要调试页面

### 核心诊断工具（保留）
- `js/button-diagnostics.js` - 核心按钮诊断，集成到监控系统
- `js/monitoring-wrapper.js` - 监控包装器，生产使用

## 🎯 清理效果

### 文件减少
- 清理 **8个JS文件** (~2MB)
- 清理 **4个测试页面** (~500KB)  
- 清理 **2个报告文档** (~100KB)

### 维护简化
- 减少重复功能模块
- 统一监控和诊断入口
- 简化文件结构

### 性能提升
- 减少不必要的脚本加载
- 简化依赖关系
- 提高页面加载速度

## ⚠️ 清理前备份

建议在清理前创建备份文件夹：
- `backup/deprecated-files/` - 存储被清理的文件
- 保留30天以防需要恢复

## 🚀 执行计划

1. **第一阶段**：清理调试和测试工具
2. **第二阶段**：清理重复测试页面  
3. **第三阶段**：清理过时文档
4. **第四阶段**：更新引用和依赖

---

*清理计划生成时间：2025年1月27日*
