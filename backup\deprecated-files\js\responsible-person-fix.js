/**
 * 负责人字段修复脚本
 * 专门解决"负责人为必填项"的验证错误
 */

(function() {
    'use strict';

    /**
     * 负责人字段修复器
     */
    class ResponsiblePersonFixer {
        constructor() {
            this.initialized = false;
        }

        /**
         * 初始化修复器
         */
        init() {
            if (this.initialized) return;

            // 等待页面加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.setup());
            } else {
                this.setup();
            }

            this.initialized = true;
        }

        /**
         * 设置修复逻辑
         */
        setup() {
            console.log('🔧 负责人字段修复器初始化...');

            // 确保负责人字段元素存在
            this.ensureResponsiblePersonField();

            // 监听登录状态变化
            this.watchLoginState();

            // 监听表单提交前的事件
            this.interceptFormSubmission();

            // 立即检查并设置负责人字段（如果已登录）
            this.checkAndSetResponsiblePerson();

            console.log('✅ 负责人字段修复器设置完成');
        }

        /**
         * 确保负责人字段元素存在
         */
        ensureResponsiblePersonField() {
            let field = document.getElementById('inchargeByBackendUserId');
            
            if (!field) {
                console.log('📝 创建隐藏的负责人字段...');
                
                field = document.createElement('input');
                field.type = 'hidden';
                field.id = 'inchargeByBackendUserId';
                field.name = 'inchargeByBackendUserId';
                
                // 将字段添加到表单中
                const form = document.getElementById('orderForm');
                if (form) {
                    form.appendChild(field);
                    console.log('✅ 负责人字段已创建并添加到表单');
                } else {
                    document.body.appendChild(field);
                    console.log('✅ 负责人字段已创建并添加到body');
                }
            } else {
                console.log('✅ 负责人字段已存在');
            }

            return field;
        }

        /**
         * 监听登录状态变化
         */
        watchLoginState() {
            // 监听AppState变化
            if (window.OTA && window.OTA.appState) {
                const appState = window.OTA.appState;
                
                // 添加登录状态监听器
                if (typeof appState.addListener === 'function') {
                    appState.addListener('auth.isLoggedIn', (isLoggedIn) => {
                        if (isLoggedIn) {
                            // 登录成功后延迟设置负责人字段
                            setTimeout(() => {
                                this.setResponsiblePersonField();
                            }, 1000);
                        }
                    });
                    console.log('✅ 登录状态监听器已设置');
                }
            }
        }

        /**
         * 拦截表单提交，确保负责人字段有值
         */
        interceptFormSubmission() {
            // 拦截创建订单按钮点击
            const createBtn = document.getElementById('createOrder');
            if (createBtn) {
                createBtn.addEventListener('click', (e) => {
                    this.ensureResponsiblePersonBeforeSubmit(e);
                }, true); // 使用捕获阶段确保优先执行
            }

            // 拦截表单提交事件
            const orderForm = document.getElementById('orderForm');
            if (orderForm) {
                orderForm.addEventListener('submit', (e) => {
                    this.ensureResponsiblePersonBeforeSubmit(e);
                }, true);
            }

            console.log('✅ 表单提交拦截器已设置');
        }

        /**
         * 在表单提交前确保负责人字段有值
         */
        ensureResponsiblePersonBeforeSubmit(event) {
            const field = document.getElementById('inchargeByBackendUserId');
            
            if (!field || !field.value) {
                console.log('⚠️ 表单提交前检测到负责人字段为空，正在修复...');
                
                // 阻止默认提交
                event.preventDefault();
                event.stopPropagation();
                
                // 强制设置负责人字段
                const success = this.forceSetResponsiblePerson();
                
                if (success) {
                    console.log('✅ 负责人字段修复成功，继续提交表单');
                    // 移除事件监听器避免无限循环
                    event.target.removeEventListener(event.type, this.ensureResponsiblePersonBeforeSubmit, true);
                    
                    // 重新触发事件
                    setTimeout(() => {
                        if (event.target.click) {
                            event.target.click();
                        } else if (event.target.submit) {
                            event.target.submit();
                        }
                    }, 100);
                } else {
                    console.error('❌ 无法修复负责人字段，表单提交被阻止');
                    alert('系统错误：无法确定负责人，请刷新页面后重试');
                }
            }
        }

        /**
         * 检查并设置负责人字段
         */
        checkAndSetResponsiblePerson() {
            // 检查是否已登录
            const appState = window.OTA && window.OTA.appState;
            if (!appState) return;

            const isLoggedIn = appState.get('auth.isLoggedIn');
            if (isLoggedIn) {
                this.setResponsiblePersonField();
            }
        }

        /**
         * 设置负责人字段值
         */
        setResponsiblePersonField() {
            const field = document.getElementById('inchargeByBackendUserId');
            if (!field) {
                console.error('❌ 负责人字段元素不存在');
                return false;
            }

            if (field.value && field.value !== '') {
                console.log('✅ 负责人字段已有值:', field.value);
                return true;
            }

            const userId = this.getResponsiblePersonId();
            if (userId) {
                field.value = userId;
                console.log('✅ 已设置负责人字段值:', userId);
                return true;
            }

            console.error('❌ 无法获取负责人ID');
            return false;
        }

        /**
         * 强制设置负责人字段（紧急情况）
         */
        forceSetResponsiblePerson() {
            const field = this.ensureResponsiblePersonField();
            
            let userId = this.getResponsiblePersonId();
            
            if (!userId) {
                // 使用紧急默认值
                userId = 1;
                console.log('⚠️ 使用紧急默认负责人ID:', userId);
            }

            field.value = userId;
            console.log('🔧 强制设置负责人字段值:', userId);
            
            return true;
        }

        /**
         * 获取负责人ID
         */
        getResponsiblePersonId() {
            try {
                // 方法1: 使用API服务
                const apiService = window.OTA && window.OTA.apiService;
                if (apiService && typeof apiService.getDefaultBackendUserId === 'function') {
                    const userId = apiService.getDefaultBackendUserId();
                    if (userId) {
                        console.log('📧 通过API服务获取负责人ID:', userId);
                        return userId;
                    }
                }

                // 方法2: 直接从系统数据匹配邮箱
                const appState = window.OTA && window.OTA.appState;
                if (appState) {
                    const currentUser = appState.get('auth.user');
                    const backendUsers = appState.get('systemData.backendUsers') || [];
                    
                    if (currentUser && currentUser.email && backendUsers.length > 0) {
                        const matched = backendUsers.find(u => 
                            (u.email || '').toLowerCase() === currentUser.email.toLowerCase()
                        );
                        
                        if (matched) {
                            console.log('📧 通过邮箱匹配获取负责人ID:', matched.id);
                            return matched.id;
                        }

                        // 使用第一个后台用户
                        const firstUserId = backendUsers[0].id;
                        console.log('📧 使用第一个后台用户ID:', firstUserId);
                        return firstUserId;
                    }
                }

                // 方法3: 硬编码映射
                const currentUser = appState && appState.get('auth.user');
                if (currentUser && currentUser.email) {
                    const userMapping = {
                        '<EMAIL>': 37,
                        '<EMAIL>': 310,
                        '<EMAIL>': 1
                    };

                    if (userMapping[currentUser.email]) {
                        console.log('📧 使用硬编码映射获取负责人ID:', userMapping[currentUser.email]);
                        return userMapping[currentUser.email];
                    }
                }

                return null;

            } catch (error) {
                console.error('❌ 获取负责人ID失败:', error);
                return null;
            }
        }

        /**
         * 运行诊断
         */
        runDiagnostics() {
            console.log('\n🔍 负责人字段诊断报告');
            console.log('='.repeat(40));

            // 检查字段存在性
            const field = document.getElementById('inchargeByBackendUserId');
            console.log(`字段存在: ${field ? '✅' : '❌'}`);
            if (field) {
                console.log(`字段值: ${field.value || '(空)'}`);
                console.log(`字段类型: ${field.type}`);
            }

            // 检查登录状态
            const appState = window.OTA && window.OTA.appState;
            const isLoggedIn = appState && appState.get('auth.isLoggedIn');
            console.log(`登录状态: ${isLoggedIn ? '✅' : '❌'}`);

            // 检查用户信息
            const currentUser = appState && appState.get('auth.user');
            if (currentUser) {
                console.log(`当前用户: ${currentUser.email || currentUser.name || '未知'}`);
            }

            // 检查后台用户数据
            const backendUsers = appState && appState.get('systemData.backendUsers');
            console.log(`后台用户数据: ${backendUsers ? `${backendUsers.length}个用户` : '❌ 未加载'}`);

            // 检查默认用户ID获取
            const userId = this.getResponsiblePersonId();
            console.log(`默认用户ID: ${userId || '❌ 无法获取'}`);

            console.log('\n🔧 修复建议:');
            if (!field) {
                console.log('- 运行 responsiblePersonFixer.ensureResponsiblePersonField()');
            }
            if (!isLoggedIn) {
                console.log('- 请先登录系统');
            }
            if (!userId) {
                console.log('- 运行 responsiblePersonFixer.forceSetResponsiblePerson()');
            }
        }
    }

    // 创建全局实例
    window.responsiblePersonFixer = new ResponsiblePersonFixer();

    // 自动初始化
    window.responsiblePersonFixer.init();

    // 提供快捷方法
    window.fixResponsiblePerson = () => {
        return window.responsiblePersonFixer.forceSetResponsiblePerson();
    };

    console.log('🔧 负责人字段修复器已加载');
    console.log('📋 可用命令:');
    console.log('  responsiblePersonFixer.runDiagnostics() - 运行诊断');
    console.log('  fixResponsiblePerson() - 强制修复负责人字段');

})();
