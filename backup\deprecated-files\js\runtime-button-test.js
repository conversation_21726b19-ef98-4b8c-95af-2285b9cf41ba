/**
 * 运行时按钮功能测试和修复脚本
 * 在浏览器控制台中运行：testButtons()
 */

function testButtons() {
    console.log('🔍 开始运行时按钮功能测试...');
    
    // 测试图片上传按钮
    testImageUploadButton();
    
    // 测试历史订单按钮
    testHistoryButton();
    
    // 测试退出登录按钮
    testLogoutButton();
    
    console.log('✅ 所有按钮测试完成');
}

function testImageUploadButton() {
    console.log('\n📸 测试图片上传按钮...');
    
    const button = document.getElementById('imageUploadButton');
    const fileInput = document.getElementById('imageFileInput');
    
    if (!button) {
        console.error('❌ 图片上传按钮不存在');
        return;
    }
    
    if (!fileInput) {
        console.error('❌ 文件输入元素不存在');
        return;
    }
    
    // 修复按钮点击事件
    const newButton = button.cloneNode(true);
    button.parentNode.replaceChild(newButton, button);
    
    newButton.addEventListener('click', function() {
        console.log('🖱️ 图片上传按钮被点击');
        fileInput.click();
    });
    
    console.log('✅ 图片上传按钮修复完成');
    console.log('🧪 点击测试：现在可以点击图片上传按钮测试功能');
}

function testHistoryButton() {
    console.log('\n📋 测试历史订单按钮...');
    
    const button = document.getElementById('historyBtn');
    const panel = document.getElementById('historyPanel');
    
    if (!button) {
        console.error('❌ 历史订单按钮不存在');
        return;
    }
    
    if (!panel) {
        console.error('❌ 历史订单面板不存在');
        return;
    }
    
    // 检查按钮是否可见
    const isVisible = button.offsetParent !== null;
    if (!isVisible) {
        console.warn('⚠️ 历史订单按钮不可见（可能用户未登录）');
    }
    
    // 修复按钮点击事件
    const newButton = button.cloneNode(true);
    button.parentNode.replaceChild(newButton, button);
    
    newButton.addEventListener('click', function() {
        console.log('🖱️ 历史订单按钮被点击');
        
        try {
            // 尝试使用OrderHistoryManager
            if (window.getOrderHistoryManager) {
                const historyManager = window.getOrderHistoryManager();
                if (historyManager && typeof historyManager.showHistoryPanel === 'function') {
                    historyManager.showHistoryPanel();
                    console.log('✅ 使用OrderHistoryManager显示面板');
                    return;
                }
            }
            
            // 备用方案：直接显示面板
            panel.style.display = 'block';
            panel.classList.remove('hidden');
            console.log('✅ 使用备用方案显示面板');
            
        } catch (error) {
            console.error('❌ 显示历史面板失败:', error);
        }
    });
    
    console.log('✅ 历史订单按钮修复完成');
    console.log('🧪 点击测试：现在可以点击历史订单按钮测试功能');
}

function testLogoutButton() {
    console.log('\n🚪 测试退出登录按钮...');
    
    const button = document.getElementById('logoutBtn');
    const loginPanel = document.getElementById('loginPanel');
    const workspace = document.getElementById('workspace');
    
    if (!button) {
        console.error('❌ 退出登录按钮不存在');
        return;
    }
    
    if (!loginPanel || !workspace) {
        console.error('❌ 必要的界面元素不存在');
        return;
    }
    
    // 检查按钮是否可见
    const isVisible = button.offsetParent !== null;
    if (!isVisible) {
        console.warn('⚠️ 退出登录按钮不可见（可能用户未登录）');
    }
    
    // 修复按钮点击事件
    const newButton = button.cloneNode(true);
    button.parentNode.replaceChild(newButton, button);
    
    newButton.addEventListener('click', function() {
        console.log('🖱️ 退出登录按钮被点击');
        
        // 显示确认对话框
        if (confirm('确认要退出登录吗？')) {
            try {
                // 清除应用状态
                if (window.OTA && window.OTA.appState && typeof window.OTA.appState.clearAuth === 'function') {
                    window.OTA.appState.clearAuth();
                    console.log('✅ 应用状态已清除');
                }
                
                // 切换界面
                loginPanel.style.display = 'block';
                workspace.style.display = 'none';
                
                // 隐藏用户信息
                const userInfo = document.querySelector('.user-info');
                if (userInfo) {
                    userInfo.style.display = 'none';
                }
                
                console.log('✅ 登出成功，已切换到登录界面');
                
            } catch (error) {
                console.error('❌ 登出过程中发生错误:', error);
            }
        }
    });
    
    console.log('✅ 退出登录按钮修复完成');
    console.log('🧪 点击测试：现在可以点击退出登录按钮测试功能');
}

// 检查当前登录状态
function checkLoginStatus() {
    console.log('\n🔐 检查当前登录状态...');
    
    const appState = window.OTA && window.OTA.appState;
    const isLoggedIn = appState && appState.get('auth.isLoggedIn');
    
    console.log(`登录状态: ${isLoggedIn ? '✅ 已登录' : '❌ 未登录'}`);
    
    if (isLoggedIn) {
        const user = appState.get('auth.user');
        console.log(`当前用户: ${user ? user.email || user.name || '未知用户' : '用户信息不可用'}`);
    }
    
    return isLoggedIn;
}

// 强制显示所有按钮（调试用）
function showAllButtons() {
    console.log('🔧 强制显示所有按钮...');
    
    const buttons = [
        'imageUploadButton',
        'historyBtn', 
        'logoutBtn'
    ];
    
    buttons.forEach(buttonId => {
        const button = document.getElementById(buttonId);
        if (button) {
            button.style.display = 'inline-block';
            button.style.visibility = 'visible';
            console.log(`✅ ${buttonId} 已强制显示`);
        } else {
            console.log(`❌ ${buttonId} 元素不存在`);
        }
    });
}

// 立即运行测试
console.log('🚀 运行时按钮诊断脚本已加载');
console.log('📋 可用命令:');
console.log('  testButtons() - 运行完整测试');
console.log('  checkLoginStatus() - 检查登录状态');
console.log('  showAllButtons() - 强制显示所有按钮');
console.log('  testImageUploadButton() - 单独测试图片上传');
console.log('  testHistoryButton() - 单独测试历史订单');
console.log('  testLogoutButton() - 单独测试退出登录');

// 如果页面已加载，自动检查登录状态
if (document.readyState === 'complete') {
    setTimeout(() => {
        checkLoginStatus();
    }, 1000);
}
