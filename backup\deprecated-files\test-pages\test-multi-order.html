<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        textarea {
            width: 100%;
            height: 200px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔢 多订单功能测试页面</h1>
        
        <div class="test-section">
            <h2>1. 基础功能测试</h2>
            <button class="test-button" onclick="testBasicFunctions()">测试基础功能</button>
            <div id="basicTest" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>2. AI检测测试</h2>
            <textarea id="testText" placeholder="输入测试文本...">
订单1：
客户：张三
电话：13800138001
上车：首都机场T3
下车：三里屯
日期：2025-01-15
时间：14:00

订单2：
客户：李四  
电话：13800138002
上车：北京站
下车：中关村
日期：2025-01-16
时间：10:30
            </textarea>
            <button class="test-button" onclick="testAIDetection()">测试AI检测</button>
            <button class="test-button" onclick="testTraditionalDetection()">测试传统检测</button>
            <div id="aiTest" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>3. 按钮功能测试</h2>
            <button class="test-button" onclick="testButtonFunctions()">测试按钮功能</button>
            <div id="buttonTest" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>4. 完整流程测试</h2>
            <button class="test-button" onclick="testFullWorkflow()">测试完整流程</button>
            <div id="fullTest" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>5. 控制台日志</h2>
            <p>请打开浏览器开发者工具的控制台查看详细日志信息</p>
            <button class="test-button" onclick="window.location.href='index.html'">返回主应用</button>
        </div>
    </div>

    <!-- 加载必要的脚本文件 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/multi-order-manager.js"></script>

    <script>
        // 等待页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔢 多订单功能测试页面已加载');
            
            // 确保必要的服务已初始化
            if (typeof getMultiOrderManager === 'function') {
                const manager = getMultiOrderManager();
                console.log('✅ 多订单管理器已加载', manager);
            } else {
                console.error('❌ 多订单管理器未加载');
            }
        });

        // 测试基础功能
        function testBasicFunctions() {
            const resultDiv = document.getElementById('basicTest');
            resultDiv.style.display = 'block';
            
            try {
                const manager = getMultiOrderManager();
                
                let results = [];
                results.push('✅ 多订单管理器实例获取成功');
                results.push(`✅ 管理器类型: ${typeof manager}`);
                results.push(`✅ 方法检查:`);
                results.push(`  - editOrder: ${typeof manager.editOrder === 'function' ? '✅' : '❌'}`);
                results.push(`  - processOrder: ${typeof manager.processOrder === 'function' ? '✅' : '❌'}`);
                results.push(`  - isMultiOrderModeAI: ${typeof manager.isMultiOrderModeAI === 'function' ? '✅' : '❌'}`);
                results.push(`  - showMultiOrderPanel: ${typeof manager.showMultiOrderPanel === 'function' ? '✅' : '❌'}`);
                
                // 检查全局暴露
                results.push(`✅ 全局暴露检查:`);
                results.push(`  - window.OTA.multiOrderManager: ${!!window.OTA?.multiOrderManager ? '✅' : '❌'}`);
                results.push(`  - window.MultiOrderManager: ${!!window.MultiOrderManager ? '✅' : '❌'}`);
                
                resultDiv.innerHTML = results.join('<br>');
                resultDiv.className = 'test-result success';
                
            } catch (error) {
                resultDiv.innerHTML = `❌ 基础功能测试失败: ${error.message}`;
                resultDiv.className = 'test-result error';
                console.error('基础功能测试错误:', error);
            }
        }

        // 测试AI检测
        async function testAIDetection() {
            const resultDiv = document.getElementById('aiTest');
            const testText = document.getElementById('testText').value;
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🤖 正在进行AI检测测试...';
            resultDiv.className = 'test-result';
            
            try {
                const manager = getMultiOrderManager();
                
                console.log('开始AI检测测试，文本长度:', testText.length);
                
                const startTime = Date.now();
                const aiResult = await manager.isMultiOrderModeAI(testText);
                const endTime = Date.now();
                
                let results = [];
                results.push(`🤖 AI检测结果: ${aiResult ? '✅ 多订单' : '❌ 单订单'}`);
                results.push(`⏱️ 检测耗时: ${endTime - startTime}ms`);
                results.push(`📝 测试文本长度: ${testText.length} 字符`);
                
                resultDiv.innerHTML = results.join('<br>');
                resultDiv.className = 'test-result success';
                
            } catch (error) {
                resultDiv.innerHTML = `❌ AI检测测试失败: ${error.message}`;
                resultDiv.className = 'test-result error';
                console.error('AI检测测试错误:', error);
            }
        }

        // 测试传统检测
        function testTraditionalDetection() {
            const resultDiv = document.getElementById('aiTest');
            const testText = document.getElementById('testText').value;
            
            try {
                const manager = getMultiOrderManager();
                
                const startTime = Date.now();
                const traditionalResult = manager.isMultiOrderModeTraditional(testText);
                const endTime = Date.now();
                
                const segments = manager.splitOrderText(testText);
                const dates = manager.detectMultipleDates(testText);
                
                let results = [];
                results.push(`🔍 传统检测结果: ${traditionalResult ? '✅ 多订单' : '❌ 单订单'}`);
                results.push(`⏱️ 检测耗时: ${endTime - startTime}ms`);
                results.push(`📊 分割片段数: ${segments.length}`);
                results.push(`📅 检测到日期数: ${dates.length}`);
                results.push(`📅 日期列表: ${dates.join(', ')}`);
                
                // 如果当前AI测试结果存在，进行对比
                const currentContent = resultDiv.innerHTML;
                if (currentContent && !currentContent.includes('传统检测结果')) {
                    resultDiv.innerHTML = currentContent + '<br><hr>' + results.join('<br>');
                } else {
                    resultDiv.innerHTML = results.join('<br>');
                }
                
                resultDiv.className = 'test-result success';
                
            } catch (error) {
                resultDiv.innerHTML = `❌ 传统检测测试失败: ${error.message}`;
                resultDiv.className = 'test-result error';
                console.error('传统检测测试错误:', error);
            }
        }

        // 测试按钮功能
        function testButtonFunctions() {
            const resultDiv = document.getElementById('buttonTest');
            resultDiv.style.display = 'block';
            
            try {
                const manager = getMultiOrderManager();
                
                let results = [];
                results.push('🔘 测试按钮功能...');
                
                // 测试editOrder方法
                console.log('测试editOrder方法调用');
                manager.editOrder(0);
                results.push('✅ editOrder(0) 调用成功');
                
                // 测试面板显示
                const testText = document.getElementById('testText').value;
                manager.showMultiOrderPanel(testText);
                results.push('✅ showMultiOrderPanel() 调用成功');
                
                // 检查面板是否显示
                const panel = document.getElementById('multiOrderPanel');
                if (panel) {
                    results.push('✅ 多订单面板元素存在');
                    results.push(`📊 面板显示状态: ${panel.style.display !== 'none' ? '显示' : '隐藏'}`);
                } else {
                    results.push('⚠️ 多订单面板元素不存在（这是正常的，因为在测试页面中）');
                }
                
                resultDiv.innerHTML = results.join('<br>');
                resultDiv.className = 'test-result success';
                
            } catch (error) {
                resultDiv.innerHTML = `❌ 按钮功能测试失败: ${error.message}`;
                resultDiv.className = 'test-result error';
                console.error('按钮功能测试错误:', error);
            }
        }

        // 测试完整流程
        async function testFullWorkflow() {
            const resultDiv = document.getElementById('fullTest');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🔄 正在执行完整流程测试...';
            
            try {
                const manager = getMultiOrderManager();
                const testText = document.getElementById('testText').value;
                
                console.log('🚀 开始完整流程测试');
                
                // 使用管理器的测试方法
                const testResult = await manager.testMultiOrderFunction(testText);
                
                let results = [];
                results.push('🔄 完整流程测试结果:');
                results.push(`✅ 测试状态: ${testResult.success ? '成功' : '失败'}`);
                
                if (testResult.success) {
                    results.push(`🤖 AI检测: ${testResult.aiDetection ? '多订单' : '单订单'}`);
                    results.push(`🔍 传统检测: ${testResult.traditionalDetection ? '多订单' : '单订单'}`);
                    results.push(`📊 分割片段: ${testResult.segmentCount} 个`);
                } else {
                    results.push(`❌ 错误信息: ${testResult.error}`);
                }
                
                resultDiv.innerHTML = results.join('<br>');
                resultDiv.className = testResult.success ? 'test-result success' : 'test-result error';
                
            } catch (error) {
                resultDiv.innerHTML = `❌ 完整流程测试失败: ${error.message}`;
                resultDiv.className = 'test-result error';
                console.error('完整流程测试错误:', error);
            }
        }
    </script>
</body>
</html>
