<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单输入调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-panel { border: 1px solid #ccc; padding: 20px; margin: 10px 0; background: #f9f9f9; }
        .test-input { width: 100%; height: 150px; margin: 10px 0; }
        .debug-output { background: #000; color: #00ff00; padding: 10px; font-family: monospace; max-height: 400px; overflow-y: auto; }
        .btn { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; border: none; }
        .btn-success { background: #28a745; color: white; border: none; }
        .btn-warning { background: #ffc107; color: black; border: none; }
        .status { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>🔍 多订单输入检测调试工具</h1>
    
    <div class="debug-panel">
        <h3>📝 测试输入区域</h3>
        <textarea id="testInput" class="test-input" placeholder="输入多订单内容进行测试...">客户：张三
电话：13800138001
上车：首都机场T3
下车：三里屯
日期：2025-01-15
时间：14:00

客户：李四  
电话：13800138002
上车：北京站
下车：中关村
日期：2025-01-16
时间：10:30</textarea>
        <br>
        <button class="btn btn-primary" onclick="testMultiOrderDetection()">🤖 测试多订单检测</button>
        <button class="btn btn-success" onclick="testInputBinding()">🔗 测试输入绑定</button>
        <button class="btn btn-warning" onclick="clearDebugOutput()">🧹 清空日志</button>
    </div>

    <div class="debug-panel">
        <h3>📊 系统状态检查</h3>
        <div id="systemStatus">检查中...</div>
        <button class="btn btn-primary" onclick="checkSystemStatus()">🔄 刷新状态</button>
    </div>

    <div class="debug-panel">
        <h3>🔍 调试输出</h3>
        <div id="debugOutput" class="debug-output">等待调试信息...</div>
    </div>

    <!-- 加载项目的JavaScript文件 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/monitoring-wrapper.js"></script>
    <script src="js/ota-channel-mapping.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/multi-order-manager.js"></script>
    <script src="js/managers/form-manager.js"></script>
    <script src="js/managers/state-manager.js"></script>
    <script src="js/ui-manager.js"></script>

    <script>
        // 调试工具脚本
        let debugMessages = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const debugOutput = document.getElementById('debugOutput');
            debugMessages.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            debugOutput.innerHTML = debugMessages.join('\n');
            debugOutput.scrollTop = debugOutput.scrollHeight;
            console.log(`[DEBUG] ${message}`);
        }

        function clearDebugOutput() {
            debugMessages = [];
            document.getElementById('debugOutput').innerHTML = '调试输出已清空...';
        }

        function checkSystemStatus() {
            const statusElement = document.getElementById('systemStatus');
            const status = [];

            // 检查基础模块
            status.push(`OTA命名空间: ${!!window.OTA ? '✅' : '❌'}`);
            status.push(`Logger: ${!!window.OTA?.logger ? '✅' : '❌'}`);
            status.push(`AppState: ${!!window.OTA?.appState ? '✅' : '❌'}`);
            status.push(`GeminiService: ${!!window.OTA?.geminiService ? '✅' : '❌'}`);
            
            // 检查多订单管理器
            status.push(`MultiOrderManager类: ${!!window.MultiOrderManager ? '✅' : '❌'}`);
            status.push(`getMultiOrderManager函数: ${!!window.getMultiOrderManager ? '✅' : '❌'}`);
            status.push(`多订单管理器实例: ${!!window.OTA?.multiOrderManager ? '✅' : '❌'}`);
            
            // 检查DOM元素
            status.push(`orderInput元素: ${!!document.getElementById('orderInput') ? '✅' : '❌'}`);
            status.push(`multiOrderPanel元素: ${!!document.getElementById('multiOrderPanel') ? '✅' : '❌'}`);

            statusElement.innerHTML = `<div class="status success">${status.join('<br>')}</div>`;
            log('系统状态检查完成');
        }

        function testInputBinding() {
            log('开始测试输入事件绑定...');
            
            try {
                const manager = window.OTA?.multiOrderManager || window.getMultiOrderManager?.();
                if (!manager) {
                    throw new Error('多订单管理器不存在');
                }

                log('多订单管理器存在，检查方法...');
                
                const methods = ['bindInputEvents', 'analyzeInputForMultiOrder', 'showMultiOrderPanel'];
                methods.forEach(method => {
                    log(`${method}: ${typeof manager[method] === 'function' ? '✅' : '❌'}`);
                });

                // 测试手动调用分析方法
                const testText = document.getElementById('testInput').value;
                if (manager.analyzeInputForMultiOrder) {
                    log('手动调用analyzeInputForMultiOrder...');
                    manager.analyzeInputForMultiOrder(testText).then(() => {
                        log('✅ 分析方法调用成功');
                    }).catch(error => {
                        log(`❌ 分析方法调用失败: ${error.message}`, 'error');
                    });
                } else {
                    log('❌ analyzeInputForMultiOrder方法不存在', 'error');
                }

            } catch (error) {
                log(`❌ 输入绑定测试失败: ${error.message}`, 'error');
            }
        }

        function testMultiOrderDetection() {
            log('开始测试多订单检测...');
            
            const testText = document.getElementById('testInput').value;
            if (!testText.trim()) {
                log('❌ 请先输入测试文本', 'error');
                return;
            }

            try {
                const geminiService = window.OTA?.geminiService;
                if (!geminiService) {
                    throw new Error('Gemini服务不存在');
                }

                log('✅ Gemini服务存在，开始检测...');
                log(`输入文本长度: ${testText.length}字符`);

                // 测试Gemini检测
                if (geminiService.detectAndSplitMultiOrders) {
                    log('调用Gemini detectAndSplitMultiOrders...');
                    geminiService.detectAndSplitMultiOrders(testText).then(result => {
                        log(`✅ Gemini检测完成:`);
                        log(`  - 是否多订单: ${result.isMultiOrder}`);
                        log(`  - 订单数量: ${result.orderCount || 0}`);
                        log(`  - 置信度: ${result.confidence || 0}`);
                        log(`  - 分析结果: ${result.analysis || 'N/A'}`);
                        
                        if (result.orders && result.orders.length > 0) {
                            log(`  - 解析的订单数: ${result.orders.length}`);
                            result.orders.forEach((order, index) => {
                                log(`    订单${index + 1}: ${order.customerName || 'N/A'} - ${order.pickup || 'N/A'} → ${order.dropoff || 'N/A'}`);
                            });
                        }
                    }).catch(error => {
                        log(`❌ Gemini检测失败: ${error.message}`, 'error');
                    });
                } else {
                    log('❌ detectAndSplitMultiOrders方法不存在', 'error');
                }

            } catch (error) {
                log(`❌ 多订单检测测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动检查系统状态
        window.addEventListener('load', () => {
            log('页面加载完成，开始系统检查...');
            setTimeout(checkSystemStatus, 1000);
        });
    </script>
</body>
</html>
