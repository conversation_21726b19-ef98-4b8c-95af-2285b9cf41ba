<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 多订单模组调试工具</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .debug-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        
        .debug-section {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 15px 0;
            padding: 20px;
        }
        
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .debug-input {
            width: 100%;
            min-height: 120px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .debug-btn {
            margin: 5px;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        
        .debug-btn:hover {
            background: #0056b3;
        }
        
        .debug-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-ok { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warn { background: #ffc107; }
        
        .dependency-check {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .log-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 多订单模组逐行调试工具</h1>
        <p>此工具用于逐步诊断为何多订单内容输入后没有触发多订单模组显示</p>
        
        <!-- 步骤1：依赖检查 -->
        <div class="debug-section">
            <h3>📋 步骤1：依赖模块加载状态检查</h3>
            <div id="dependencyStatus" class="dependency-check">
                检查中...
            </div>
            <button class="debug-btn" onclick="checkDependencies()">🔄 重新检查依赖</button>
        </div>
        
        <!-- 步骤2：输入事件绑定检查 -->
        <div class="debug-section">
            <h3>📝 步骤2：输入事件监听器检查</h3>
            <div id="inputEventStatus" class="dependency-check">
                检查中...
            </div>
            <button class="debug-btn" onclick="checkInputEvents()">🔄 检查输入事件</button>
            <button class="debug-btn" onclick="forceBindEvents()">🔗 强制重新绑定事件</button>
        </div>
        
        <!-- 步骤3：手动触发分析 -->
        <div class="debug-section">
            <h3>🤖 步骤3：手动触发多订单分析</h3>
            <textarea id="debugInput" class="debug-input" placeholder="输入测试文本...">[2025/7/10 17:27] Joshua: 接机：

团号：EJBTBY250712-1
2PAX
13/7 KLIA IN 0100 (MF857) - MOXY PUTRAJAYA
客人：朱芸 
客人联系：18130403306

[2025/7/11 19:22] Joshua: 送机：

团号：EJBTBY250710-1
2PAX
14/7 THE FACE STYLE HOTEL KL 0500AM - KLIA2 (AK5136 0915)
客人：刘凯
客人联系：18764221412

[2025/7/11 19:22] Joshua: 接机：

团号：EJBTBY250715
2PAX
15/7 KLIA2 IN 2015 (AK181) - MOXY PUTRAJAYA
客人：顾婉婷 & 苟晓琼
客人联系：13884407028</textarea>
            
            <div>
                <button class="debug-btn" onclick="testDirectAnalysis()">🔍 直接分析</button>
                <button class="debug-btn" onclick="testGeminiCall()">🤖 测试Gemini调用</button>
                <button class="debug-btn" onclick="testPanelShow()">📱 测试面板显示</button>
                <button class="debug-btn" onclick="clearResults()">🧹 清空结果</button>
            </div>
        </div>
        
        <!-- 步骤4：结果显示 -->
        <div class="debug-section">
            <h3>📊 调试结果</h3>
            <div id="debugResults" class="debug-result">
                等待调试结果...
            </div>
        </div>
        
        <!-- 步骤5：实时日志 -->
        <div class="debug-section">
            <h3>📜 实时日志输出</h3>
            <div id="logOutput" class="log-output">
                等待日志输出...
            </div>
            <button class="debug-btn" onclick="clearLogs()">🧹 清空日志</button>
        </div>
    </div>

    <!-- 加载所有必要的脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/monitoring-wrapper.js"></script>
    <script src="js/ota-channel-mapping.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/order-history-manager.js"></script>
    <script src="js/image-upload-manager.js"></script>
    <script src="js/currency-converter.js"></script>
    <script src="js/multi-order-manager.js"></script>

    <script>
        // 全局调试状态
        let debugState = {
            logs: [],
            lastAnalysisResult: null,
            isRunning: false
        };

        // 重写console.log来捕获日志
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function appendLog(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            debugState.logs.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateLogDisplay();
            
            // 调用原始方法
            if (type === 'error') originalError(...args);
            else if (type === 'warn') originalWarn(...args);
            else originalLog(...args);
        }

        console.log = (...args) => appendLog('log', ...args);
        console.error = (...args) => appendLog('error', ...args);
        console.warn = (...args) => appendLog('warn', ...args);

        // 更新日志显示
        function updateLogDisplay() {
            const logOutput = document.getElementById('logOutput');
            if (logOutput) {
                logOutput.textContent = debugState.logs.slice(-50).join('\n');
                logOutput.scrollTop = logOutput.scrollHeight;
            }
        }

        // 检查依赖模块
        function checkDependencies() {
            const dependencies = [
                { name: 'Utils工具库', check: () => window.utils },
                { name: 'Logger日志系统', check: () => window.logger || window.OTA?.logger },
                { name: 'AppState状态管理', check: () => window.appState || window.OTA?.appState },
                { name: 'GeminiService AI服务', check: () => window.geminiService || window.OTA?.geminiService },
                { name: 'MultiOrderManager多订单管理器', check: () => window.MultiOrderManager },
                { name: '多订单管理器实例', check: () => window.getMultiOrderManager },
                { name: '多订单管理器实例运行', check: () => {
                    try {
                        const instance = window.getMultiOrderManager();
                        return instance && typeof instance.analyzeInputForMultiOrder === 'function';
                    } catch (e) {
                        return false;
                    }
                }}
            ];

            let html = '';
            let allOk = true;

            dependencies.forEach(dep => {
                const isOk = dep.check();
                const status = isOk ? 'ok' : 'error';
                allOk = allOk && isOk;
                
                html += `<div><span class="status-indicator status-${status}"></span>${dep.name}: ${isOk ? '✅ 正常' : '❌ 异常'}</div>`;
            });

            html += `<div style="margin-top: 10px; font-weight: bold;">总体状态: ${allOk ? '✅ 所有依赖正常' : '❌ 存在异常依赖'}</div>`;
            
            document.getElementById('dependencyStatus').innerHTML = html;
            
            console.log('依赖检查完成:', { allOk, dependencies: dependencies.map(d => ({name: d.name, ok: d.check()})) });
        }

        // 检查输入事件
        function checkInputEvents() {
            const orderInput = document.getElementById('orderInput');
            const hasOrderInput = !!orderInput;
            
            let eventListeners = [];
            let manager = null;
            
            try {
                manager = window.getMultiOrderManager();
                if (manager) {
                    // 检查是否已绑定事件
                    const hasEvents = manager.debounceTimer !== undefined;
                    eventListeners.push(`防抖计时器: ${hasEvents ? '已设置' : '未设置'}`);
                }
            } catch (e) {
                eventListeners.push(`管理器访问错误: ${e.message}`);
            }

            const html = `
                <div><span class="status-indicator status-${hasOrderInput ? 'ok' : 'error'}"></span>orderInput元素: ${hasOrderInput ? '存在' : '不存在'}</div>
                <div><span class="status-indicator status-${manager ? 'ok' : 'error'}"></span>多订单管理器: ${manager ? '可访问' : '不可访问'}</div>
                ${eventListeners.map(item => `<div><span class="status-indicator status-warn"></span>${item}</div>`).join('')}
            `;

            document.getElementById('inputEventStatus').innerHTML = html;
            
            console.log('输入事件检查:', { hasOrderInput, manager: !!manager, eventListeners });
        }

        // 强制重新绑定事件
        function forceBindEvents() {
            try {
                const manager = window.getMultiOrderManager();
                if (manager && typeof manager.bindInputEvents === 'function') {
                    manager.bindInputEvents();
                    console.log('✅ 强制重新绑定事件成功');
                    checkInputEvents(); // 重新检查
                } else {
                    console.error('❌ 无法访问bindInputEvents方法');
                }
            } catch (e) {
                console.error('❌ 强制绑定事件失败:', e);
            }
        }

        // 直接测试分析
        async function testDirectAnalysis() {
            const input = document.getElementById('debugInput').value;
            if (!input.trim()) {
                alert('请输入测试文本');
                return;
            }

            updateResults('🔍 开始直接分析测试...');

            try {
                const manager = window.getMultiOrderManager();
                if (!manager) {
                    throw new Error('多订单管理器不可用');
                }

                console.log('开始直接调用analyzeInputForMultiOrder方法...');
                
                await manager.analyzeInputForMultiOrder(input);
                
                updateResults('✅ 直接分析调用完成，检查控制台日志和面板状态');
                
            } catch (error) {
                console.error('直接分析测试失败:', error);
                updateResults(`❌ 直接分析失败: ${error.message}`);
            }
        }

        // 测试Gemini调用
        async function testGeminiCall() {
            const input = document.getElementById('debugInput').value;
            if (!input.trim()) {
                alert('请输入测试文本');
                return;
            }

            updateResults('🤖 开始Gemini AI调用测试...');

            try {
                const geminiService = window.geminiService || window.OTA?.geminiService;
                if (!geminiService) {
                    throw new Error('Gemini服务不可用');
                }

                console.log('开始调用Gemini detectAndSplitMultiOrders方法...');
                
                const result = await geminiService.detectAndSplitMultiOrders(input);
                
                console.log('Gemini调用结果:', result);
                debugState.lastAnalysisResult = result;
                
                updateResults(`✅ Gemini调用成功:
多订单: ${result.isMultiOrder}
订单数量: ${result.orderCount || 0}
置信度: ${result.confidence || 0}
orders数组长度: ${result.orders ? result.orders.length : 0}

完整结果已保存到调试状态中，可在控制台查看`);
                
            } catch (error) {
                console.error('Gemini调用测试失败:', error);
                updateResults(`❌ Gemini调用失败: ${error.message}`);
            }
        }

        // 测试面板显示
        function testPanelShow() {
            try {
                const manager = window.getMultiOrderManager();
                if (!manager) {
                    throw new Error('多订单管理器不可用');
                }

                // 使用上次的分析结果或创建测试数据
                let testOrders = debugState.lastAnalysisResult?.orders;
                
                if (!testOrders || testOrders.length === 0) {
                    testOrders = [
                        {
                            rawText: "测试订单1",
                            customerName: "张三",
                            customerContact: "13800138000",
                            pickup: "机场",
                            dropoff: "酒店",
                            pickupDate: "2025-07-15",
                            pickupTime: "09:00",
                            passengerCount: 2,
                            subCategoryId: 2,
                            carTypeId: 5
                        },
                        {
                            rawText: "测试订单2", 
                            customerName: "李四",
                            customerContact: "13900139000",
                            pickup: "酒店",
                            dropoff: "机场",
                            pickupDate: "2025-07-16",
                            pickupTime: "10:00",
                            passengerCount: 3,
                            subCategoryId: 3,
                            carTypeId: 5
                        }
                    ];
                }

                console.log('测试显示多订单面板，订单数据:', testOrders);
                
                manager.showMultiOrderPanel(testOrders);
                
                updateResults(`✅ 面板显示测试完成
使用订单数: ${testOrders.length}
检查页面是否显示了多订单面板`);
                
            } catch (error) {
                console.error('面板显示测试失败:', error);
                updateResults(`❌ 面板显示测试失败: ${error.message}`);
            }
        }

        // 更新结果显示
        function updateResults(message) {
            const resultsDiv = document.getElementById('debugResults');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.textContent += `[${timestamp}] ${message}\n\n`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // 清空结果
        function clearResults() {
            document.getElementById('debugResults').textContent = '等待调试结果...';
        }

        // 清空日志
        function clearLogs() {
            debugState.logs = [];
            updateLogDisplay();
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            console.log('🔍 多订单调试工具已加载');
            
            // 延迟检查依赖，确保所有脚本加载完成
            setTimeout(() => {
                checkDependencies();
                checkInputEvents();
            }, 1000);
        });

        // 暴露调试状态到全局，方便控制台访问
        window.debugState = debugState;
    </script>
</body>
</html>
