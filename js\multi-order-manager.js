/**
 * 多订单处理管理器 - 重构优化版 v2.0
 * 负责多订单检测、智能分割、批量处理和UI交互管理
 * 重点优化：代码清理、AI分析增强、UI交互改进、批量处理
 * <AUTHOR>
 * @version 2.0.0
 */

// 防止重复加载
if (window.MultiOrderManager) {
    console.log('重构版多订单管理器已存在，跳过重复加载');
} else {

// 获取依赖模块（延迟获取以确保加载顺序）
function getLogger() {
    return window.OTA?.logger || window.logger;
}

function getGeminiService() {
    return window.OTA?.geminiService || window.geminiService;
}

function getApiService() {
    return window.OTA?.apiService || window.apiService;
}

function getAppState() {
    return window.OTA?.appState || window.appState;
}

function getUiManager() {
    return window.OTA?.uiManager || window.uiManager;
}

class MultiOrderManager {
    constructor() {
        // 1. 配置和模式
        this.config = {
            minInputLength: 50,           // 最小输入长度启动检测
            debounceDelay: 1200,          // 防抖延迟
            maxOrdersPerBatch: 5,         // 每批次最大订单数
            batchDelay: 800,              // 批次间延迟（ms）
            confidenceThreshold: 0.7      // AI检测置信度阈值
        };

        // 2. 状态管理
        this.state = {
            isAnalyzing: false,
            currentSegments: [],
            selectedOrders: new Set(),
            processedOrders: new Map(),
            batchProgress: {
                total: 0,
                completed: 0,
                failed: 0,
                isRunning: false
            }
        };

        // 4. 事件监听器
        this.eventListeners = new Map();
        this.debounceTimer = null;
        
        this.init();
    }

    /**
     * 初始化管理器
     */
    init() {
        const logger = getLogger();
        logger?.log('多订单管理器v2.0正在初始化...', 'info');

        this.setupEventListeners();
        this.setupInputListener();
        this.initPanelEvents();
        
        logger?.log('多订单管理器v2.0初始化完成', 'success');
    }

    /**
     * 设置全局事件监听器
     */
    setupEventListeners() {
        // 保存 this 引用以确保正确的上下文
        const self = this;
        
        // 监听多订单检测事件 - 使用箭头函数确保 this 绑定
        document.addEventListener('multiOrderDetected', (event) => {
            try {
                const { data, orderText } = event.detail;
                getLogger()?.log('🔔 收到多订单检测事件', 'info', { 
                    dataType: Array.isArray(data) ? 'array' : typeof data,
                    hasOrderText: !!orderText 
                });
                self.handleMultiOrderDetection(data, orderText);
            } catch (error) {
                getLogger()?.logError('多订单事件处理失败', error);
            }
        });

        // 监听应用状态变化
        document.addEventListener('appStateChanged', (event) => {
            try {
                if (event.detail.key === 'currentOrder') {
                    self.handleOrderStateChange(event.detail.value);
                }
            } catch (error) {
                getLogger()?.logError('应用状态变化处理失败', error);
            }
        });

        getLogger()?.log('多订单事件监听器已设置', 'info');
    }

    /**
     * 处理多订单检测事件
     * @param {object} data - 订单数据
     * @param {string} orderText - 订单文本
     */
    handleMultiOrderDetection(data, orderText) {
        const logger = getLogger();
        logger?.log('🔄 处理多订单检测事件', 'info', { 
            dataType: Array.isArray(data) ? 'array' : typeof data,
            orderLength: orderText?.length || 0 
        });

        try {
            // 处理不同格式的数据
            let processedData = data;
            
            // 如果是对象，尝试转换为数组
            if (data && typeof data === 'object' && !Array.isArray(data)) {
                // 检查是否有 orders 属性
                if (data.orders && Array.isArray(data.orders)) {
                    processedData = data.orders;
                }
                // 检查是否有 segments 属性
                else if (data.segments && Array.isArray(data.segments)) {
                    processedData = data.segments;
                }
                // 检查是否有 items 属性
                else if (data.items && Array.isArray(data.items)) {
                    processedData = data.items;
                }
                // 如果对象有多个键值对，可能每个键都是一个订单
                else {
                    const keys = Object.keys(data);
                    if (keys.length > 1) {
                        processedData = keys.map(key => ({
                            id: key,
                            ...data[key]
                        }));
                    }
                }
            }

            // 统一的多订单处理逻辑
            if (Array.isArray(processedData) && processedData.length > 1) {
                logger?.log(`检测到${processedData.length}个订单，显示多订单面板`, 'info');
                this.showMultiOrderPanel(processedData);
            } else if (Array.isArray(processedData) && processedData.length === 1) {
                logger?.log('检测到单个订单，进行智能分析', 'info');
                this.analyzeInputForMultiOrder(orderText);
            } else {
                logger?.log('无有效数据或无法识别格式，进行智能分析', 'info');
                // 检查是否包含多个时间点或地点，使用智能分析
                this.analyzeInputForMultiOrder(orderText);
            }
        } catch (error) {
            logger?.logError('处理多订单检测事件失败', error);
        }
    }

    /**
     * 处理订单状态变化
     * @param {object} orderData - 订单数据
     */
    handleOrderStateChange(orderData) {
        const logger = getLogger();
        logger?.log('🔄 处理订单状态变化', 'info', orderData);

        try {
            // 如果有当前订单数据，可以进行额外的处理
            if (orderData) {
                // 这里可以添加订单状态变化的处理逻辑
                // 例如：更新UI显示、保存到历史记录等
                logger?.log('订单状态已更新', 'success');
            }
        } catch (error) {
            logger?.logError('处理订单状态变化失败', error);
        }
    }

    /**
     * 设置输入事件监听器
     */
    setupInputListener() {
        // 确保DOM元素存在后再绑定事件
        const bindEvents = () => {
            const orderInput = document.getElementById('orderInput');
            if (orderInput) {
                this.bindInputEvents();
                getLogger()?.log('✅ 输入事件监听器已成功绑定', 'success');
            } else {
                getLogger()?.log('⚠️ orderInput元素不存在，等待DOM加载...', 'warn');
                // 如果元素不存在，等待一段时间后重试
                setTimeout(bindEvents, 1000);
            }
        };

        // 立即尝试绑定
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', bindEvents);
        } else {
            bindEvents();
        }
    }

    /**
     * 绑定输入事件（优化版）
     */
    bindInputEvents() {
        const orderInput = document.getElementById('orderInput');
        if (!orderInput) {
            getLogger()?.log('订单输入框不存在，跳过事件绑定', 'warn');
            return;
        }

        // 防抖处理的输入事件
        const handleInput = (event) => {
            if (this.debounceTimer) {
                clearTimeout(this.debounceTimer);
            }
            
            this.debounceTimer = setTimeout(async () => {
                if (this.state.isAnalyzing) return;
                
                try {
                    this.state.isAnalyzing = true;
                    await this.analyzeInputForMultiOrder(event.target.value);
                } catch (error) {
                    getLogger()?.logError('多订单分析失败', error);
                } finally {
                    this.state.isAnalyzing = false;
                }
            }, this.config.debounceDelay);
        };

        // 绑定事件
        orderInput.addEventListener('input', handleInput);
        orderInput.addEventListener('paste', (event) => {
            setTimeout(() => handleInput(event), 100);
        });

        getLogger()?.log('输入事件监听器已绑定', 'info');
    }

    /**
     * 分析输入内容是否为多订单（核心检测逻辑）
     * @param {string} text - 输入文本
     */
    async analyzeInputForMultiOrder(text) {
        if (!text || text.trim().length < this.config.minInputLength) {
            this.hideMultiOrderPanel();
            return;
        }

        const logger = getLogger();
        logger?.log(`🔍 开始分析输入内容，长度: ${text.length}字符`, 'info');
        
        try {
            // 直接调用Gemini服务进行一体化解析
            logger?.log('🤖 使用一体化Gemini AI进行多订单检测和完整解析...', 'info');
            
            const geminiService = getGeminiService();
            if (!geminiService) {
                throw new Error('Gemini AI服务不可用');
            }
            
            const geminiResult = await geminiService.detectAndSplitMultiOrders(text);
            
            // 添加详细的调试日志
            logger?.log('🔍 Gemini返回结果详情:', 'info', {
                isMultiOrder: geminiResult.isMultiOrder,
                hasOrders: !!geminiResult.orders,
                ordersLength: geminiResult.orders ? geminiResult.orders.length : 0,
                hasSegments: !!geminiResult.segments,
                segmentsLength: geminiResult.segments ? geminiResult.segments.length : 0,
                fullResult: geminiResult
            });
            
            if (geminiResult.isMultiOrder && geminiResult.orders && geminiResult.orders.length > 1) {
                logger?.log(`✅ Gemini检测到多订单: ${geminiResult.orders.length}个完整订单`, 'success', {
                    confidence: geminiResult.confidence,
                    analysis: geminiResult.analysis,
                    orderCount: geminiResult.orderCount
                });
                
                // 传递完整解析的订单对象数组
                logger?.log('📋 准备显示多订单面板...', 'info');
                this.showMultiOrderPanel(geminiResult.orders);
                logger?.log('✅ 多订单面板显示完成', 'success');
            } else {
                logger?.log('📋 Gemini检测为单订单，隐藏多订单面板', 'info', {
                    reason: !geminiResult.isMultiOrder ? '非多订单' : 
                           !geminiResult.orders ? '缺少orders数组' : 
                           geminiResult.orders.length <= 1 ? '订单数量不足' : '未知原因'
                });
                this.hideMultiOrderPanel();
            }
        } catch (error) {
            logger?.logError('Gemini一体化分析失败', error);
            // 异常情况下隐藏面板
            this.hideMultiOrderPanel();
        }
    }

    /**
     * 传统多订单检测（基于模式匹配）
     * @param {string} text - 输入文本
     * @returns {object} 检测结果
     */
    detectMultiOrderTraditional(text) {
        if (!text || typeof text !== 'string') {
            return { isMultiOrder: false, confidence: 0, reason: '文本无效' };
        }

        const cleanText = text.trim();
        let score = 0;
        const reasons = [];

        // 1. 检查明确的订单标识
        const orderMarkers = [
            /订单\s*[：:]\s*\d+/gi,
            /order\s*[：:]\s*\d+/gi,
            /第\s*\d+\s*个?订单/gi
        ];
        
        for (const pattern of orderMarkers) {
            const matches = cleanText.match(pattern);
            if (matches && matches.length > 1) {
                score += 40;
                reasons.push(`发现${matches.length}个订单标识`);
                break;
            }
        }

        // 2. 检查数字列表模式
        const numberListPattern = /^\s*\d+\s*[、.]/gm;
        const numberMatches = cleanText.match(numberListPattern);
        if (numberMatches && numberMatches.length > 1) {
            score += 30;
            reasons.push(`发现${numberMatches.length}个数字列表项`);
        }

        // 3. 检查日期时间密度
        const dateTimePatterns = [
            /\d{4}[-\/]\d{1,2}[-\/]\d{1,2}/g,
            /\d{1,2}[:：]\d{2}/g,
            /(今天|明天|后天)/g
        ];
        
        let totalDateTimeMatches = 0;
        dateTimePatterns.forEach(pattern => {
            const matches = cleanText.match(pattern) || [];
            totalDateTimeMatches += matches.length;
        });
        
        if (totalDateTimeMatches > 2) {
            score += 20;
            reasons.push(`发现${totalDateTimeMatches}个时间日期`);
        }

        // 4. 检查分隔符
        const separators = [
            /\n\s*[-=]{3,}\s*\n/g,
            /\n\s*\n\s*\n/g
        ];
        
        for (const pattern of separators) {
            const matches = cleanText.match(pattern);
            if (matches && matches.length > 0) {
                score += 15;
                reasons.push('发现明确分隔符');
                break;
            }
        }

        // 5. 文本长度和复杂度分析
        const lines = cleanText.split('\n').filter(line => line.trim().length > 10);
        if (lines.length > 8) {
            score += 10;
            reasons.push(`文本有${lines.length}行，可能包含多个订单`);
        }

        const confidence = Math.min(score / 100, 1);
        const isMultiOrder = confidence > 0.3;

        return {
            isMultiOrder,
            confidence,
            score,
            reasons: reasons.join('; ')
        };
    }

    /**
     * AI多订单检测（直接调用Gemini服务）
     * @param {string} text - 输入文本
     * @returns {Promise<object>} AI检测结果
     */
    async detectMultiOrderAI(text) {
        const logger = getLogger();
        
        try {
            const geminiService = getGeminiService();
            if (!geminiService) {
                throw new Error('Gemini AI服务不可用');
            }
            
            // 直接调用Gemini服务
            const result = await geminiService.detectAndSplitMultiOrders(text);
            
            // 返回兼容的格式
            return {
                isMultiOrder: result.isMultiOrder || false,
                confidence: result.confidence || 0,
                orderCount: result.orderCount || 1,
                reason: result.analysis || '完全由Gemini AI处理'
            };
        } catch (error) {
            logger?.logError('AI多订单检测失败', error);
            return { 
                isMultiOrder: false, 
                confidence: 0, 
                orderCount: 1,
                reason: `AI检测失败: ${error.message}` 
            };
        }
    }


    /**
     * 智能分割订单文本（完全由Gemini处理）
     * @param {string} text - 输入文本
     * @returns {Promise<Array>} 分割后的订单对象数组
     */
    async smartSplitOrderText(text) {
        if (!text || typeof text !== 'string') {
            return [text || ''];
        }

        const logger = getLogger();
        
        try {
            const geminiService = getGeminiService();
            if (!geminiService) {
                throw new Error('Gemini AI服务不可用');
            }
            
            // 直接使用Gemini AI完全处理分割
            const result = await geminiService.detectAndSplitMultiOrders(text);
            
            // 更新当前分段状态 - 现在存储完整的订单对象
            this.state.currentSegments = result.orders || [text];
            
            logger?.log(`📋 Gemini智能分割完成: ${result.orders ? result.orders.length : 1} 个完整订单`, 'success', {
                isMultiOrder: result.isMultiOrder,
                confidence: result.confidence,
                analysis: result.analysis
            });
            
            return result.orders || [text];
            
        } catch (error) {
            logger?.logError('Gemini智能分割失败，返回原文本', error);
            return [text];
        }
    }

    /**
     * 显示多订单面板（适配新的浮窗结构）
     * @param {Array} orders - 完整解析的订单对象数组
     */
    showMultiOrderPanel(orders) {
        const logger = getLogger();
        logger?.log('🔄 开始显示多订单浮窗面板...', 'info');
        
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        if (!multiOrderPanel) {
            logger?.log('❌ 多订单面板元素不存在', 'error');
            return;
        }

        logger?.log(`📋 准备显示${orders.length}个订单`, 'info');

        // 存储订单数据到状态
        this.state.parsedOrders = orders;
        
        // 更新面板内容
        logger?.log('🔧 更新面板内容...', 'info');
        this.updatePanelContent(orders);
        
        // 显示浮窗面板 - 使用新的CSS类结构
        logger?.log('👁️ 显示浮窗面板...', 'info');
        multiOrderPanel.classList.remove('hidden');
        
        // 触发显示动画
        requestAnimationFrame(() => {
            multiOrderPanel.style.display = 'flex';
        });

        // 重置状态
        this.state.selectedOrders.clear();
        this.state.processedOrders.clear();
        
        // 默认选中所有订单
        orders.forEach((_, index) => {
            this.state.selectedOrders.add(index);
        });
        
        this.updateSelectedCount();
        this.updateOrderStats(orders);

        // 确保面板可见和居中
        this.ensurePanelVisible();

        // 启用浮窗增强功能
        this.addPanelDragFeature();
        this.addPanelToggleFeature();

        logger?.log(`✅ 多订单浮窗面板已显示，包含${orders.length}个完整订单`, 'success');
    }

    /**
     * 隐藏多订单面板（适配浮窗结构）
     */
    hideMultiOrderPanel() {
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        if (multiOrderPanel) {
            // 添加淡出动画
            multiOrderPanel.style.animation = 'multiOrderPanelHide 0.2s ease-in forwards';
            
            setTimeout(() => {
                multiOrderPanel.classList.add('hidden');
                multiOrderPanel.style.display = 'none';
                multiOrderPanel.style.animation = '';
            }, 200);
            
            getLogger()?.log('🚪 多订单浮窗面板已关闭', 'info');
        }
    }

    /**
     * 更新面板内容（显示完整解析的订单对象）
     * @param {Array} orders - 完整解析的订单对象数组
     */
    updatePanelContent(orders) {
        const orderList = document.querySelector('#multiOrderPanel .multi-order-list');
        if (!orderList) {
            getLogger()?.log('多订单列表容器不存在', 'warn');
            return;
        }

        // 生成订单项HTML - 显示结构化字段
        const orderItemsHTML = orders.map((order, index) => {
            const orderId = `order-${index}`;
            
            // 生成订单摘要
            const summary = this.generateOrderSummary(order);
            
            return `
                <div class="order-item" data-order-index="${index}">
                    <div class="order-header">
                        <div class="order-selector">
                            <input type="checkbox" id="${orderId}" checked>
                            <label for="${orderId}">订单 ${index + 1}</label>
                        </div>
                        <div class="order-status">
                            <span class="status-badge status-parsed">已解析</span>
                        </div>
                    </div>
                    <div class="order-content">
                        <div class="order-summary">
                            ${summary}
                        </div>
                        <div class="order-fields-grid" style="display: none;">
                            ${this.generateOrderFieldsHTML(order, index)}
                        </div>
                    </div>
                    <div class="order-actions">
                        <button type="button" class="btn btn-sm btn-outline toggle-details-btn" 
                                onclick="window.OTA.multiOrderManager.toggleOrderDetails(${index})" 
                                title="展开/收起详细字段">
                            📋 详情
                        </button>
                        <button type="button" class="btn btn-sm btn-outline edit-order-btn" 
                                onclick="window.OTA.multiOrderManager.editOrder(${index})" 
                                title="编辑此订单">
                            ✏️ 编辑
                        </button>
                        <button type="button" class="btn btn-sm btn-primary process-order-btn" 
                                onclick="window.OTA.multiOrderManager.processOrder(${index})" 
                                title="使用AI解析此订单">
                            🤖 解析
                        </button>
                        <button type="button" class="btn btn-sm btn-success preview-order-btn" 
                                onclick="window.OTA.multiOrderManager.previewOrder(${index})" 
                                title="预览订单详情" style="display: none;">
                            👁️ 预览
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        orderList.innerHTML = orderItemsHTML;

        // 绑定选择框事件
        const checkboxes = orderList.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateSelectedCount();
            });
        });

        // 更新统计信息
        this.updateOrderStats(orders.length);
    }

    /**
     * 更新订单统计信息
     * @param {number} orderCount - 订单数量
     */
    updateOrderStats(orderCount) {
        const countElement = document.getElementById('multiOrderCount');
        const dateRangeElement = document.getElementById('multiOrderDateRange');
        
        if (countElement) {
            countElement.textContent = `${orderCount} 个订单`;
        }
        
        if (dateRangeElement) {
            dateRangeElement.textContent = '待解析';
        }
    }

    /**
     * 更新选中订单数量显示
     */
    updateSelectedCount() {
        const checkboxes = document.querySelectorAll('.order-item input[type="checkbox"]:checked');
        const countElement = document.getElementById('selectedOrderCount');
        if (countElement) {
            countElement.textContent = `已选择 ${checkboxes.length} 个订单`;
        }
    }

    /**
     * 编辑指定订单
     * @param {number} index - 订单索引
     */
    editOrder(index) {
        const logger = getLogger();
        logger?.log(`编辑订单 ${index + 1}`, 'info');

        try {
            if (index < 0 || index >= this.state.currentSegments.length) {
                throw new Error(`订单索引 ${index} 超出范围`);
            }

            const orderInput = document.getElementById('orderInput');
            if (!orderInput) {
                throw new Error('订单输入框不存在');
            }

            // 将选中的订单片段填充到主输入框
            orderInput.value = this.state.currentSegments[index].trim();
            
            // 隐藏多订单面板
            this.hideMultiOrderPanel();
            
            // 滚动到输入框并聚焦
            orderInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
            orderInput.focus();
            
            // 触发输入事件以启动实时分析
            orderInput.dispatchEvent(new Event('input', { bubbles: true }));

            logger?.log(`订单 ${index + 1} 已载入编辑器`, 'success');

        } catch (error) {
            logger?.logError(`编辑订单失败: ${error.message}`, error);
            getUiManager()?.showError(`编辑订单失败: ${error.message}`);
        }
    }

    /**
     * 处理指定订单（AI解析）
     * @param {number} index - 订单索引
     */
    async processOrder(index) {
        const logger = getLogger();
        logger?.log(`开始处理订单 ${index + 1}`, 'info');

        try {
            if (index < 0 || index >= this.state.currentSegments.length) {
                throw new Error(`订单索引 ${index} 超出范围`);
            }

            const orderText = this.state.currentSegments[index].trim();
            
            // 更新UI状态
            this.updateOrderProcessingStatus(index, 'processing');

            // 调用Gemini AI分析
            const geminiService = getGeminiService();
            if (!geminiService || !geminiService.isAvailable()) {
                throw new Error('Gemini AI服务不可用');
            }

            const result = await geminiService.parseOrder(orderText);
            
            if (!result || !result.success) {
                throw new Error(result?.message || '订单解析失败');
            }

            // 存储解析结果
            this.state.processedOrders.set(index, {
                rawText: orderText,
                parsedData: result.data,
                confidence: result.confidence || 0,
                timestamp: Date.now()
            });

            // 更新应用状态
            const appState = getAppState();
            if (appState) {
                appState.setCurrentOrder({
                    rawText: orderText,
                    parsedData: result.data,
                    confidence: result.confidence || 0,
                    source: 'multi-order-single',
                    orderIndex: index
                });
            }

            // 更新UI
            this.updateOrderProcessingStatus(index, 'success');
            this.showOrderDetails(index, result.data);

            logger?.log(`订单 ${index + 1} 处理完成`, 'success', {
                confidence: result.confidence,
                dataKeys: Object.keys(result.data || {})
            });

        } catch (error) {
            logger?.logError(`处理订单 ${index + 1} 失败: ${error.message}`, error);
            this.updateOrderProcessingStatus(index, 'error', error.message);
            getUiManager()?.showError(`处理订单失败: ${error.message}`);
        }
    }

    /**
     * 预览订单详情
     * @param {number} index - 订单索引
     */
    previewOrder(index) {
        const processedOrder = this.state.processedOrders.get(index);
        if (!processedOrder) {
            getUiManager()?.showError('订单未解析，请先点击解析按钮');
            return;
        }

        const detailsElement = document.querySelector(`.order-item[data-order-index="${index}"] .order-details`);
        if (detailsElement) {
            detailsElement.style.display = detailsElement.style.display === 'none' ? 'block' : 'none';
        }
    }

    /**
     * 更新订单处理状态
     * @param {number} index - 订单索引
     * @param {string} status - 状态：processing, success, error
     * @param {string} message - 额外信息
     */
    updateOrderProcessingStatus(index, status, message = '') {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem) return;

        const statusBadge = orderItem.querySelector('.status-badge');
        const processBtn = orderItem.querySelector('.process-order-btn');
        const previewBtn = orderItem.querySelector('.preview-order-btn');

        if (statusBadge) {
            statusBadge.className = 'status-badge';
            switch (status) {
                case 'processing':
                    statusBadge.classList.add('status-processing');
                    statusBadge.textContent = '解析中...';
                    break;
                case 'success':
                    statusBadge.classList.add('status-success');
                    statusBadge.textContent = '已解析';
                    break;
                case 'error':
                    statusBadge.classList.add('status-error');
                    statusBadge.textContent = '解析失败';
                    break;
                default:
                    statusBadge.classList.add('status-pending');
                    statusBadge.textContent = '待处理';
            }
        }

        if (processBtn) {
            switch (status) {
                case 'processing':
                    processBtn.disabled = true;
                    processBtn.textContent = '🔄 解析中...';
                    break;
                case 'success':
                    processBtn.disabled = false;
                    processBtn.textContent = '✅ 重新解析';
                    processBtn.classList.remove('btn-primary');
                    processBtn.classList.add('btn-outline');
                    break;
                case 'error':
                    processBtn.disabled = false;
                    processBtn.textContent = '🔄 重试';
                    processBtn.classList.remove('btn-primary');
                    processBtn.classList.add('btn-warning');
                    break;
                default:
                    processBtn.disabled = false;
                    processBtn.textContent = '🤖 解析';
            }
        }

        if (previewBtn && status === 'success') {
            previewBtn.style.display = 'inline-block';
        }

        if (message && status === 'error') {
            const orderContent = orderItem.querySelector('.order-content');
            if (orderContent) {
                const errorDiv = orderContent.querySelector('.error-message') || document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.innerHTML = `<small style="color: #dc3545;">错误: ${message}</small>`;
                if (!orderContent.contains(errorDiv)) {
                    orderContent.appendChild(errorDiv);
                }
            }
        }
    }

    /**
     * 显示订单详情
     * @param {number} index - 订单索引
     * @param {object} orderData - 解析后的订单数据
     */
    showOrderDetails(index, orderData) {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem) return;

        const detailsElement = orderItem.querySelector('.order-details');
        if (!detailsElement) return;

        // 生成详情HTML
        const detailsHTML = `
            <div class="order-fields">
                <div class="field-group">
                    <div class="field">
                        <label>客户姓名:</label>
                        <span class="editable-field" data-field="customer_name">${orderData.customer_name || 'N/A'}</span>
                    </div>
                    <div class="field">
                        <label>联系电话:</label>
                        <span class="editable-field" data-field="customer_contact">${orderData.customer_contact || 'N/A'}</span>
                    </div>
                </div>
                <div class="field-group">
                    <div class="field">
                        <label>上车地点:</label>
                        <span class="editable-field" data-field="pickup">${orderData.pickup || 'N/A'}</span>
                    </div>
                    <div class="field">
                        <label>下车地点:</label>
                        <span class="editable-field" data-field="dropoff">${orderData.dropoff || 'N/A'}</span>
                    </div>
                </div>
                <div class="field-group">
                    <div class="field">
                        <label>日期:</label>
                        <span class="editable-field" data-field="pickup_date">${orderData.pickup_date || 'N/A'}</span>
                    </div>
                    <div class="field">
                        <label>时间:</label>
                        <span class="editable-field" data-field="pickup_time">${orderData.pickup_time || 'N/A'}</span>
                    </div>
                </div>
                <div class="field-group">
                    <div class="field">
                        <label>乘客数:</label>
                        <span class="editable-field" data-field="passenger_count">${orderData.passenger_count || 'N/A'}</span>
                    </div>
                    <div class="field">
                        <label>服务类型:</label>
                        <span>${this.getServiceTypeName(orderData.sub_category_id)}</span>
                    </div>
                </div>
                ${orderData.flight_info ? `
                <div class="field-group">
                    <div class="field">
                        <label>航班号:</label>
                        <span>${orderData.flight_info}</span>
                    </div>
                    <div class="field">
                        <label>航班时间:</label>
                        <span>${orderData.arrival_time || orderData.departure_time || 'N/A'}</span>
                    </div>
                </div>
                ` : ''}
            </div>
            <div class="order-actions-inline">
                <button type="button" class="btn btn-sm btn-outline" onclick="window.OTA.multiOrderManager.editOrderFields(${index})">
                    ✏️ 编辑字段
                </button>
                <button type="button" class="btn btn-sm btn-success" onclick="window.OTA.multiOrderManager.createSingleOrder(${index})">
                    🚀 创建此订单
                </button>
            </div>
        `;

        detailsElement.innerHTML = detailsHTML;
    }

    /**
     * 获取服务类型名称
     * @param {number} subCategoryId - 服务类型ID
     * @returns {string} 服务类型名称
     */
    getServiceTypeName(subCategoryId) {
        const serviceTypes = {
            2: '接机服务',
            3: '送机服务',
            4: '包车服务'
        };
        return serviceTypes[subCategoryId] || '未知服务';
    }

    /**
     * 编辑订单字段（内联编辑）
     * @param {number} index - 订单索引
     */
    editOrderFields(index) {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem) return;

        const editableFields = orderItem.querySelectorAll('.editable-field');
        editableFields.forEach(field => {
            const currentValue = field.textContent.trim();
            const fieldName = field.getAttribute('data-field');
            
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentValue === 'N/A' ? '' : currentValue;
            input.className = 'field-input';
            input.setAttribute('data-field', fieldName);
            
            input.addEventListener('blur', () => {
                this.saveFieldEdit(index, fieldName, input.value);
                field.textContent = input.value || 'N/A';
                field.style.display = 'inline';
                input.remove();
            });
            
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    input.blur();
                }
            });
            
            field.style.display = 'none';
            field.parentNode.insertBefore(input, field.nextSibling);
            input.focus();
        });
    }

    /**
     * 保存字段编辑
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     * @param {string} value - 新值
     */
    saveFieldEdit(index, fieldName, value) {
        const processedOrder = this.state.processedOrders.get(index);
        if (processedOrder) {
            processedOrder.parsedData[fieldName] = value;
            this.state.processedOrders.set(index, processedOrder);
            getLogger()?.log(`订单 ${index + 1} 字段 ${fieldName} 已更新`, 'info');
        }
    }

    /**
     * 创建单个订单
     * @param {number} index - 订单索引
     */
    async createSingleOrder(index) {
        const processedOrder = this.state.processedOrders.get(index);
        if (!processedOrder) {
            getUiManager()?.showError('订单未解析，无法创建');
            return;
        }

        const logger = getLogger();
        const apiService = getApiService();
        
        if (!apiService) {
            getUiManager()?.showError('API服务不可用');
            return;
        }

        try {
            logger?.log(`开始创建订单 ${index + 1}`, 'info');
            
            // 验证订单数据
            const validation = apiService.validateOrderData(processedOrder.parsedData);
            if (!validation.isValid) {
                getUiManager()?.showValidationErrors(validation.errors);
                return;
            }

            const result = await apiService.createOrder(processedOrder.parsedData);
            
            if (result.success) {
                const orderId = result.data?.id || result.data?.order_id || result.id || 'N/A';
                logger?.log(`订单 ${index + 1} 创建成功`, 'success', { orderId });
                getUiManager()?.showSimpleSuccessToast(orderId);
                
                // 更新状态为已创建
                this.updateOrderProcessingStatus(index, 'created');
                
                // 记录到历史
                try {
                    const historyManager = window.OTA?.orderHistoryManager || window.orderHistoryManager;
                    if (historyManager) {
                        historyManager.addOrder(processedOrder.parsedData, orderId, result);
                    }
                } catch (historyError) {
                    logger?.logError('记录订单历史失败', historyError);
                }
            } else {
                const errorMessage = result.message || result.error || '订单创建失败';
                logger?.log(`订单 ${index + 1} 创建失败`, 'error', { error: errorMessage });
                getUiManager()?.showError(`订单创建失败: ${errorMessage}`);
            }
        } catch (error) {
            logger?.logError(`创建订单 ${index + 1} 异常`, error);
            getUiManager()?.showError(`创建订单异常: ${error.message}`);
        }
    }

    /**
     * 初始化面板事件
     */
    initPanelEvents() {
        // 关闭按钮
        const closeBtn = document.getElementById('closeMultiOrderBtn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hideMultiOrderPanel();
                getLogger()?.log('多订单面板已关闭', 'info');
            });
        }

        // 全选/取消全选
        const selectAllBtn = document.getElementById('selectAllOrdersBtn');
        const deselectAllBtn = document.getElementById('deselectAllOrdersBtn');
        
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', () => this.selectAllOrders(true));
        }
        
        if (deselectAllBtn) {
            deselectAllBtn.addEventListener('click', () => this.selectAllOrders(false));
        }

        // 验证全部
        const validateAllBtn = document.getElementById('validateAllOrdersBtn');
        if (validateAllBtn) {
            validateAllBtn.addEventListener('click', () => this.processAllOrders());
        }

        // 批量创建
        const batchCreateBtn = document.getElementById('batchCreateBtn');
        const createSelectedBtn = document.getElementById('createSelectedOrdersBtn');
        
        if (batchCreateBtn) {
            batchCreateBtn.addEventListener('click', () => this.handleBatchCreate());
        }
        
        if (createSelectedBtn) {
            createSelectedBtn.addEventListener('click', () => this.createSelectedOrders());
        }

        // 点击面板外部关闭
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        if (multiOrderPanel) {
            multiOrderPanel.addEventListener('click', (event) => {
                if (event.target === multiOrderPanel) {
                    this.hideMultiOrderPanel();
                }
            });
        }

        // 添加面板拖拽和最小化/最大化功能
        this.addPanelDragFeature();
        this.addPanelToggleFeature();

        getLogger()?.log('多订单面板事件已初始化', 'info');
    }

    /**
     * 选择/取消选择所有订单
     * @param {boolean} selected - 是否选中
     */
    selectAllOrders(selected) {
        const checkboxes = document.querySelectorAll('.order-item input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selected;
        });
        this.updateSelectedCount();
        getLogger()?.log(`${selected ? '全选' : '取消全选'}订单`, 'info');
    }

    /**
     * 处理所有订单（批量解析）
     */
    async processAllOrders() {
        const logger = getLogger();
        logger?.log('开始批量解析所有订单', 'info');

        const orderItems = document.querySelectorAll('.order-item');
        const total = orderItems.length;
        
        this.state.batchProgress = {
            total,
            completed: 0,
            failed: 0,
            isRunning: true
        };

        this.updateBatchProgress();

        for (let i = 0; i < total; i++) {
            try {
                await this.processOrder(i);
                this.state.batchProgress.completed++;
            } catch (error) {
                logger?.logError(`批量解析订单 ${i + 1} 失败`, error);
                this.state.batchProgress.failed++;
            }
            
            this.updateBatchProgress();
            
            // 添加延迟避免API过载
            if (i < total - 1) {
                await new Promise(resolve => setTimeout(resolve, this.config.batchDelay));
            }
        }

        this.state.batchProgress.isRunning = false;
        this.updateBatchProgress();

        logger?.log(`批量解析完成，成功: ${this.state.batchProgress.completed}, 失败: ${this.state.batchProgress.failed}`, 'success');
    }

    /**
     * 更新批量进度显示
     */
    updateBatchProgress() {
        const statusElement = document.querySelector('.batch-create-status');
        if (!statusElement) return;

        const { total, completed, failed, isRunning } = this.state.batchProgress;
        
        if (isRunning) {
            statusElement.innerHTML = `
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${(completed + failed) / total * 100}%"></div>
                </div>
                <div class="progress-text">
                    解析进度: ${completed + failed}/${total} (成功: ${completed}, 失败: ${failed})
                </div>
            `;
            statusElement.style.display = 'block';
        } else {
            statusElement.style.display = 'none';
        }
    }

    /**
     * 批量创建处理
     */
    async handleBatchCreate() {
        // 先解析所有订单
        await this.processAllOrders();
        
        // 然后创建所有已解析的订单
        await this.createSelectedOrders();
    }

    /**
     * 创建选中的订单
     */
    async createSelectedOrders() {
        const logger = getLogger();
        const checkboxes = document.querySelectorAll('.order-item input[type="checkbox"]:checked');
        
        if (checkboxes.length === 0) {
            getUiManager()?.showError('没有选中的订单');
            return;
        }

        logger?.log(`开始批量创建 ${checkboxes.length} 个选中订单`, 'info');

        let successCount = 0;
        let failedCount = 0;

        for (const checkbox of checkboxes) {
            const orderItem = checkbox.closest('.order-item');
            const index = parseInt(orderItem.getAttribute('data-order-index'));
            
            try {
                // 确保订单已解析
                if (!this.state.processedOrders.has(index)) {
                    await this.processOrder(index);
                }
                
                await this.createSingleOrder(index);
                successCount++;
            } catch (error) {
                logger?.logError(`创建订单 ${index + 1} 失败`, error);
                failedCount++;
            }
            
            // 添加延迟避免API过载
            await new Promise(resolve => setTimeout(resolve, this.config.batchDelay));
        }

        const message = `批量创建完成！成功: ${successCount}, 失败: ${failedCount}`;
        if (failedCount === 0) {
            getUiManager()?.showAlert(message, 'success');
        } else {
            getUiManager()?.showAlert(message, 'warning');
        }

        logger?.log(message, 'info');
    }

    /**
     * Chrome MCP 集成接口 - 重构版增强
     * 用于与Chrome Model Context Protocol集成测试
     */
    initChromeMCPIntegration() {
        const logger = getLogger();
        logger?.log('🌐 初始化Chrome MCP集成接口...', 'info');

        // Chrome MCP 接口对象
        this.chromeMCP = {
            // 内容提取接口
            extractContent: async (url) => {
                try {
                    logger?.log(`🔍 Chrome MCP: 尝试提取 ${url} 的内容`, 'info');
                    
                    // 检查是否在Chrome MCP环境中
                    if (typeof window.chrome !== 'undefined' && window.chrome.runtime) {
                        // 实际的Chrome MCP调用将在这里实现
                        logger?.log('⚠️ Chrome MCP: 真实环境调用需要MCP环境支持', 'warning');
                        return { success: false, message: 'Chrome MCP环境未就绪' };
                    } else {
                        // 模拟模式用于测试
                        logger?.log('🧪 Chrome MCP: 模拟模式运行', 'info');
                        return {
                            success: true,
                            content: '模拟提取的页面内容：订单信息...',
                            url: url,
                            timestamp: Date.now()
                        };
                    }
                } catch (error) {
                    logger?.logError('Chrome MCP内容提取失败', error);
                    return { success: false, error: error.message };
                }
            },

            // 截图接口
            captureScreenshot: async (options = {}) => {
                try {
                    logger?.log('📷 Chrome MCP: 尝试截图', 'info');
                    
                    if (typeof window.chrome !== 'undefined' && window.chrome.runtime) {
                        logger?.log('⚠️ Chrome MCP: 真实截图需要MCP环境支持', 'warning');
                        return { success: false, message: 'Chrome MCP环境未就绪' };
                    } else {
                        logger?.log('🧪 Chrome MCP: 模拟截图模式', 'info');
                        return {
                            success: true,
                            screenshot: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
                            options: options,
                            timestamp: Date.now()
                        };
                    }
                } catch (error) {
                    logger?.logError('Chrome MCP截图失败', error);
                    return { success: false, error: error.message };
                }
            },

            // 自动化交互接口
            automateInteraction: async (actions) => {
                try {
                    logger?.log(`🤖 Chrome MCP: 执行 ${actions.length} 个自动化操作`, 'info');
                    
                    if (typeof window.chrome !== 'undefined' && window.chrome.runtime) {
                        logger?.log('⚠️ Chrome MCP: 真实自动化需要MCP环境支持', 'warning');
                        return { success: false, message: 'Chrome MCP环境未就绪' };
                    } else {
                        logger?.log('🧪 Chrome MCP: 模拟自动化操作', 'info');
                        const results = actions.map((action, index) => ({
                            actionIndex: index,
                            type: action.type || 'unknown',
                            status: 'simulated',
                            success: true
                        }));
                        
                        return {
                            success: true,
                            results: results,
                            timestamp: Date.now()
                        };
                    }
                } catch (error) {
                    logger?.logError('Chrome MCP自动化失败', error);
                    return { success: false, error: error.message };
                }
            },

            // 检查MCP环境状态
            checkMCPStatus: () => {
                const hasChrome = typeof window.chrome !== 'undefined';
                const hasRuntime = hasChrome && !!window.chrome.runtime;
                const hasMCP = hasRuntime && !!window.chrome.runtime.sendMessage;
                
                const status = {
                    chromeAPI: hasChrome,
                    runtime: hasRuntime,
                    mcpReady: hasMCP,
                    environment: hasMCP ? 'production' : 'simulation'
                };
                
                logger?.log(`🔍 Chrome MCP状态检查:`, 'info', status);
                return status;
            }
        };

        logger?.log('✅ Chrome MCP集成接口初始化完成', 'success');
        return this.chromeMCP;
    }

    /**
     * 测试Chrome MCP集成功能
     */
    async testChromeMCPIntegration() {
        const logger = getLogger();
        logger?.log('🧪 开始测试Chrome MCP集成功能', 'info');

        try {
            // 确保MCP接口已初始化
            if (!this.chromeMCP) {
                this.initChromeMCPIntegration();
            }

            // 测试状态检查
            const status = this.chromeMCP.checkMCPStatus();
            logger?.log(`📊 MCP环境状态: ${status.environment}`, 'info');

            // 测试内容提取
            const contentResult = await this.chromeMCP.extractContent('https://example.com');
            logger?.log(`🔍 内容提取测试: ${contentResult.success ? '成功' : '失败'}`, 
                contentResult.success ? 'success' : 'warning');

            // 测试截图功能
            const screenshotResult = await this.chromeMCP.captureScreenshot({ 
                width: 800, 
                height: 600 
            });
            logger?.log(`📷 截图测试: ${screenshotResult.success ? '成功' : '失败'}`, 
                screenshotResult.success ? 'success' : 'warning');

            // 测试自动化交互
            const automationResult = await this.chromeMCP.automateInteraction([
                { type: 'click', selector: '.btn-test' },
                { type: 'input', selector: '#test-input', value: 'test' }
            ]);
            logger?.log(`🤖 自动化测试: ${automationResult.success ? '成功' : '失败'}`, 
                automationResult.success ? 'success' : 'warning');

            const testResult = {
                status: status,
                contentExtraction: contentResult.success,
                screenshot: screenshotResult.success,
                automation: automationResult.success,
                overallSuccess: true
            };

            logger?.log('🎉 Chrome MCP集成测试完成', 'success', testResult);
            return testResult;

        } catch (error) {
            logger?.logError('Chrome MCP集成测试失败', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 生成订单摘要信息
     * @param {Object} order - 订单对象
     * @returns {string} 摘要HTML
     */
    generateOrderSummary(order) {
        const customerName = order.customerName || '未知客户';
        const pickup = order.pickup || '未指定';
        const dropoff = order.dropoff || '未指定';
        const pickupDate = order.pickupDate || '未指定';
        const pickupTime = order.pickupTime || '未指定';
        const passengerCount = order.passengerCount || 1;
        const price = order.otaPrice ? `${order.currency || 'MYR'} ${order.otaPrice}` : '未指定';
        
        return `
            <div class="order-summary-grid">
                <div class="summary-item">
                    <label>客户:</label>
                    <span>${customerName}</span>
                </div>
                <div class="summary-item">
                    <label>路线:</label>
                    <span>${pickup} → ${dropoff}</span>
                </div>
                <div class="summary-item">
                    <label>时间:</label>
                    <span>${pickupDate} ${pickupTime}</span>
                </div>
                <div class="summary-item">
                    <label>乘客:</label>
                    <span>${passengerCount}人</span>
                </div>
                <div class="summary-item">
                    <label>价格:</label>
                    <span>${price}</span>
                </div>
            </div>
        `;
    }

    /**
     * 生成订单详细字段HTML
     * @param {Object} order - 订单对象
     * @param {number} index - 订单索引
     * @returns {string} 字段HTML
     */
    generateOrderFieldsHTML(order, index) {
        return `
            <div class="order-fields-container">
                <div class="fields-row">
                    <div class="field-group">
                        <label>客户姓名</label>
                        <input type="text" name="customerName" value="${order.customerName || ''}" 
                               onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'customerName', this.value)">
                    </div>
                    <div class="field-group">
                        <label>联系电话</label>
                        <input type="tel" name="customerContact" value="${order.customerContact || ''}"
                               onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'customerContact', this.value)">
                    </div>
                    <div class="field-group">
                        <label>客户邮箱</label>
                        <input type="email" name="customerEmail" value="${order.customerEmail || ''}"
                               onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'customerEmail', this.value)">
                    </div>
                </div>
                
                <div class="fields-row">
                    <div class="field-group">
                        <label>上车地点</label>
                        <input type="text" name="pickup" value="${order.pickup || ''}"
                               onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'pickup', this.value)">
                    </div>
                    <div class="field-group">
                        <label>目的地</label>
                        <input type="text" name="dropoff" value="${order.dropoff || ''}"
                               onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'dropoff', this.value)">
                    </div>
                </div>
                
                <div class="fields-row">
                    <div class="field-group">
                        <label>接送日期</label>
                        <input type="date" name="pickupDate" value="${order.pickupDate || ''}"
                               onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'pickupDate', this.value)">
                    </div>
                    <div class="field-group">
                        <label>接送时间</label>
                        <input type="time" name="pickupTime" value="${order.pickupTime || ''}"
                               onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'pickupTime', this.value)">
                    </div>
                    <div class="field-group">
                        <label>乘客人数</label>
                        <input type="number" name="passengerCount" value="${order.passengerCount || 1}" min="1" max="50"
                               onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'passengerCount', this.value)">
                    </div>
                    <div class="field-group">
                        <label>行李件数</label>
                        <input type="number" name="luggageCount" value="${order.luggageCount || 1}" min="0" max="50"
                               onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'luggageCount', this.value)">
                    </div>
                </div>
                
                <div class="fields-row">
                    <div class="field-group">
                        <label>航班信息</label>
                        <input type="text" name="flightInfo" value="${order.flightInfo || ''}"
                               onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'flightInfo', this.value)">
                    </div>
                    <div class="field-group">
                        <label>OTA订单号</label>
                        <input type="text" name="otaReferenceNumber" value="${order.otaReferenceNumber || ''}"
                               onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'otaReferenceNumber', this.value)">
                    </div>
                </div>
                
                <div class="fields-row">
                    <div class="field-group">
                        <label>价格</label>
                        <div class="price-group">
                            <input type="number" name="otaPrice" value="${order.otaPrice || ''}" step="0.01" min="0"
                                   onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'otaPrice', this.value)">
                            <select name="currency" onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'currency', this.value)">
                                <option value="MYR" ${order.currency === 'MYR' ? 'selected' : ''}>MYR</option>
                                <option value="USD" ${order.currency === 'USD' ? 'selected' : ''}>USD</option>
                                <option value="SGD" ${order.currency === 'SGD' ? 'selected' : ''}>SGD</option>
                                <option value="CNY" ${order.currency === 'CNY' ? 'selected' : ''}>CNY</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="fields-row">
                    <div class="field-group">
                        <label>额外要求</label>
                        <textarea name="extraRequirement" rows="3" 
                                  onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'extraRequirement', this.value)">${order.extraRequirement || ''}</textarea>
                    </div>
                </div>
                
                <div class="fields-row">
                    <div class="field-group checkbox-group">
                        <label>
                            <input type="checkbox" name="babyChair" ${order.babyChair ? 'checked' : ''}
                                   onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'babyChair', this.checked)">
                            儿童座椅
                        </label>
                        <label>
                            <input type="checkbox" name="tourGuide" ${order.tourGuide ? 'checked' : ''}
                                   onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'tourGuide', this.checked)">
                            导游服务
                        </label>
                        <label>
                            <input type="checkbox" name="meetAndGreet" ${order.meetAndGreet ? 'checked' : ''}
                                   onchange="window.OTA.multiOrderManager.updateOrderField(${index}, 'meetAndGreet', this.checked)">
                            迎接服务
                        </label>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 切换订单详情显示/隐藏
     * @param {number} index - 订单索引
     */
    toggleOrderDetails(index) {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem) return;

        const fieldsGrid = orderItem.querySelector('.order-fields-grid');
        const toggleBtn = orderItem.querySelector('.toggle-details-btn');
        
        if (!fieldsGrid || !toggleBtn) return;

        const isHidden = fieldsGrid.style.display === 'none';
        
        if (isHidden) {
            fieldsGrid.style.display = 'block';
            toggleBtn.innerHTML = '📄 收起';
            toggleBtn.title = '收起详细字段';
        } else {
            fieldsGrid.style.display = 'none';
            toggleBtn.innerHTML = '📋 详情';
            toggleBtn.title = '展开详细字段';
        }
        
        getLogger()?.log(`订单 ${index + 1} 详情${isHidden ? '展开' : '收起'}`, 'info');
    }

    /**
     * 更新订单字段值
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     * @param {*} value - 字段值
     */
    updateOrderField(index, fieldName, value) {
        if (!this.state.parsedOrders || index >= this.state.parsedOrders.length) {
            getLogger()?.log(`无效的订单索引: ${index}`, 'warn');
            return;
        }

        // 更新状态中的订单数据
        this.state.parsedOrders[index][fieldName] = value;
        
        // 更新摘要显示
        this.updateOrderSummaryDisplay(index);
        
        getLogger()?.log(`订单 ${index + 1} 的 ${fieldName} 已更新为: ${value}`, 'info');
    }

    /**
     * 更新订单摘要显示
     * @param {number} index - 订单索引
     */
    updateOrderSummaryDisplay(index) {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem || !this.state.parsedOrders[index]) return;

        const order = this.state.parsedOrders[index];
        const summaryDiv = orderItem.querySelector('.order-summary');
        
        if (summaryDiv) {
            summaryDiv.innerHTML = this.generateOrderSummary(order);
        }
    }

    /**
     * 确保面板在视窗范围内可见
     */
    ensurePanelVisible() {
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        if (!multiOrderPanel) return;

        // 确保面板居中显示在视窗中
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // 获取面板的实际尺寸
        const panelRect = multiOrderPanel.getBoundingClientRect();
        
        // 如果面板超出视窗范围，调整其大小
        let adjustments = {};
        
        if (panelRect.width > viewportWidth * 0.95) {
            adjustments.width = '95vw';
        }
        
        if (panelRect.height > viewportHeight * 0.9) {
            adjustments.height = '90vh';
        }
        
        // 应用调整
        Object.assign(multiOrderPanel.style, adjustments);
        
        // 滚动到面板位置（如果需要）
        multiOrderPanel.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center',
            inline: 'center'
        });
        
        getLogger()?.log('📱 多订单面板位置已优化', 'info', {
            viewport: `${viewportWidth}x${viewportHeight}`,
            panel: `${panelRect.width}x${panelRect.height}`,
            adjustments
        });
    }

    /**
     * 添加面板拖拽功能（增强浮窗体验）
     */
    addPanelDragFeature() {
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        const header = multiOrderPanel?.querySelector('.multi-order-header');
        
        if (!multiOrderPanel || !header) return;

        let isDragging = false;
        let currentX = 0;
        let currentY = 0;
        let initialX = 0;
        let initialY = 0;

        header.style.cursor = 'move';
        header.title = '拖拽面板来移动位置';

        const dragStart = (e) => {
            if (e.target.closest('button')) return; // 避免按钮干扰拖拽
            
            initialX = e.clientX - currentX;
            initialY = e.clientY - currentY;
            
            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
                multiOrderPanel.style.transition = 'none';
            }
        };

        const dragEnd = (e) => {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
            multiOrderPanel.style.transition = 'all var(--transition-normal)';
        };

        const drag = (e) => {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
                
                // 限制拖拽范围在视窗内
                const rect = multiOrderPanel.getBoundingClientRect();
                const maxX = window.innerWidth - rect.width;
                const maxY = window.innerHeight - rect.height;
                
                currentX = Math.max(0, Math.min(currentX, maxX));
                currentY = Math.max(0, Math.min(currentY, maxY));
                
                multiOrderPanel.style.transform = `translate(${currentX}px, ${currentY}px)`;
            }
        };

        header.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);

        getLogger()?.log('🖱️ 面板拖拽功能已启用', 'info');
    }

    /**
     * 添加面板最小化/最大化功能
     */
    addPanelToggleFeature() {
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        const header = multiOrderPanel?.querySelector('.multi-order-header');
        
        if (!multiOrderPanel || !header) return;

        // 添加最小化按钮
        const headerActions = header.querySelector('.header-actions');
        if (headerActions) {
            const toggleBtn = document.createElement('button');
            toggleBtn.type = 'button';
            toggleBtn.className = 'btn btn-icon';
            toggleBtn.innerHTML = '📐';
            toggleBtn.title = '最小化/最大化';
            toggleBtn.id = 'togglePanelSizeBtn';

            let isMinimized = false;
            let originalHeight = '';

            toggleBtn.addEventListener('click', () => {
                const content = multiOrderPanel.querySelector('.multi-order-content');
                const list = multiOrderPanel.querySelector('.multi-order-list');
                const footer = multiOrderPanel.querySelector('.multi-order-footer');
                
                if (!isMinimized) {
                    // 最小化 - 只显示标题栏
                    originalHeight = multiOrderPanel.style.height || '80vh';
                    list.style.display = 'none';
                    footer.style.display = 'none';
                    multiOrderPanel.style.height = 'auto';
                    toggleBtn.innerHTML = '📊';
                    toggleBtn.title = '最大化';
                    isMinimized = true;
                } else {
                    // 最大化 - 恢复完整显示
                    list.style.display = 'block';
                    footer.style.display = 'flex';
                    multiOrderPanel.style.height = originalHeight;
                    toggleBtn.innerHTML = '📐';
                    toggleBtn.title = '最小化';
                    isMinimized = false;
                }
            });

            // 插入到关闭按钮之前
            const closeBtn = headerActions.querySelector('#closeMultiOrderBtn');
            if (closeBtn) {
                headerActions.insertBefore(toggleBtn, closeBtn);
            } else {
                headerActions.appendChild(toggleBtn);
            }
        }

        getLogger()?.log('📐 面板切换功能已启用', 'info');
    }

    // ...existing code...
}

// 全局实例变量
let multiOrderManagerInstance = null;

/**
 * 创建全局实例
 * @returns {MultiOrderManager} 管理器实例
 */
function getMultiOrderManager() {
    if (!multiOrderManagerInstance) {
        multiOrderManagerInstance = new MultiOrderManager();
    }
    return multiOrderManagerInstance;
}

// 导出到全局作用域
window.MultiOrderManager = MultiOrderManager;
window.getMultiOrderManager = getMultiOrderManager;

// 确保OTA命名空间存在并暴露管理器
window.OTA = window.OTA || {};
window.OTA.MultiOrderManager = MultiOrderManager;
window.OTA.getMultiOrderManager = getMultiOrderManager;
window.OTA.multiOrderManager = getMultiOrderManager();

// 向后兼容
window.multiOrderManager = getMultiOrderManager();

// 结束防重复加载检查
}
