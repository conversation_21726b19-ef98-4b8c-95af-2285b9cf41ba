/**
 * 历史订单管理器
 * 负责订单历史记录的存储、检索和管理
 * <AUTHOR>
 * @version 1.0.0
 */

// 获取依赖模块（延迟获取以确保加载顺序）
function getLogger() {
    return window.OTA && window.OTA.logger || window.logger;
}

function getAppState() {
    return window.OTA && window.OTA.appState || window.appState;
}

class OrderHistoryManager {
    constructor() {
        this.storageKey = 'ota_order_history';
        this.maxHistorySize = 1000; // 最大存储1000条历史记录
        this.currentUserEmail = null; // 当前用户邮箱
        this.init();
    }

    /**
     * 初始化历史订单管理器
     */
    init() {
        // 迁移旧数据格式到新的按账号存储格式
        this.migrateOldData();

        // 确保localStorage中有历史记录对象
        const historyData = localStorage.getItem(this.storageKey);
        if (!historyData) {
            localStorage.setItem(this.storageKey, JSON.stringify({}));
        }

        // 初始化事件监听器
        this.initEventListeners();

        const logger = getLogger();
        if (logger) {
            logger.log('历史订单管理器已初始化（按账号存储）', 'info');
        }
    }

    /**
     * 迁移旧数据格式到新格式
     * 将数组格式转换为按账号存储的对象格式
     */
    migrateOldData() {
        try {
            const existingData = localStorage.getItem(this.storageKey);
            if (existingData) {
                const parsed = JSON.parse(existingData);

                // 如果是旧的数组格式，进行迁移
                if (Array.isArray(parsed) && parsed.length > 0) {
                    const migratedData = {
                        '<EMAIL>': parsed // 将旧数据存储到特殊账号下
                    };
                    localStorage.setItem(this.storageKey, JSON.stringify(migratedData));

                    const logger = getLogger();
                    if (logger) {
                        logger.log(`已迁移 ${parsed.length} 条历史订单数据到新格式`, 'info');
                    }
                }
            }
        } catch (error) {
            const logger = getLogger();
            if (logger) {
                logger.log(`数据迁移失败: ${error.message}`, 'error');
            }
        }
    }

    /**
     * 设置当前用户邮箱
     * @param {string} email - 用户邮箱
     */
    setCurrentUser(email) {
        this.currentUserEmail = email;
        const logger = getLogger();
        if (logger) {
            logger.log(`历史订单管理器已切换到用户: ${email}`, 'info');
        }
    }

    /**
     * 获取当前用户邮箱
     * @returns {string} 当前用户邮箱
     */
    getCurrentUser() {
        if (!this.currentUserEmail) {
            // 尝试从应用状态获取当前用户
            const appState = getAppState();
            if (appState) {
                this.currentUserEmail = appState.get('auth.user.email') || '<EMAIL>';
            } else {
                this.currentUserEmail = '<EMAIL>';
            }
        }
        return this.currentUserEmail;
    }

    /**
     * 添加新的订单记录
     * @param {Object} orderData - 订单数据
     * @param {string} orderId - GoMyHire API返回的订单ID
     * @param {Object} apiResponse - 完整的API响应
     */
    addOrder(orderData, orderId, apiResponse = null) {
        try {
            const historyRecord = {
                // 基础信息
                id: this.generateHistoryId(),
                orderId: orderId,
                timestamp: new Date().toISOString(),
                
                // 订单内容
                orderData: {
                    // 基本信息
                    subCategoryId: orderData.subCategoryId || '',
                    otaReferenceNumber: orderData.otaReferenceNumber || '',
                    otaChannel: orderData.otaChannel || '',
                    carTypeId: orderData.carTypeId || '',
                    
                    // 客户信息
                    customerName: orderData.customerName || '',
                    customerContact: orderData.customerContact || '',
                    customerEmail: orderData.customerEmail || '',
                    flightInfo: orderData.flightInfo || '',
                    
                    // 行程信息
                    pickup: orderData.pickup || '',
                    destination: orderData.destination || '',
                    date: orderData.date || '',
                    time: orderData.time || '',
                    passengerNumber: orderData.passengerNumber || '',
                    luggageNumber: orderData.luggageNumber || '',
                    
                    // 其他信息
                    specialRequests: orderData.specialRequests || '',
                    otaPrice: orderData.otaPrice || '',
                    drivingRegionId: orderData.drivingRegionId || '',
                    languagesIdArray: orderData.languagesIdArray || {}
                },
                
                // 元数据
                metadata: {
                    userEmail: getAppState() ? getAppState().get('auth.user.email') : '',
                    createdBy: getAppState() ? getAppState().get('auth.user.name') : '',
                    apiResponse: apiResponse ? {
                        success: apiResponse.success,
                        message: apiResponse.message,
                        data: apiResponse.data
                    } : null
                }
            };

            // 获取当前用户和所有历史记录
            const currentUser = this.getCurrentUser();
            const allHistory = this.getAllUsersHistory();
            const userHistory = allHistory[currentUser] || [];

            // 添加新记录到开头
            userHistory.unshift(historyRecord);

            // 限制历史记录数量
            if (userHistory.length > this.maxHistorySize) {
                userHistory.splice(this.maxHistorySize);
            }

            // 更新用户的历史记录
            allHistory[currentUser] = userHistory;

            // 保存到localStorage
            localStorage.setItem(this.storageKey, JSON.stringify(allHistory));
            
            const logger = getLogger();
            if (logger) {
                logger.log(`订单历史记录已添加: ${orderId}`, 'success');
            }
            
            return historyRecord;
            
        } catch (error) {
            const logger = getLogger();
            if (logger) {
                logger.log(`添加订单历史记录失败: ${error.message}`, 'error');
            }
            throw error;
        }
    }

    /**
     * 获取当前用户的历史订单
     * @returns {Array} 历史订单数组
     */
    getHistory() {
        try {
            const currentUser = this.getCurrentUser();
            const historyJson = localStorage.getItem(this.storageKey);
            const allHistory = historyJson ? JSON.parse(historyJson) : {};

            // 返回当前用户的历史记录，如果不存在则返回空数组
            return allHistory[currentUser] || [];
        } catch (error) {
            const logger = getLogger();
            if (logger) {
                logger.log(`获取历史订单失败: ${error.message}`, 'error');
            }
            return [];
        }
    }

    /**
     * 获取所有用户的历史订单（管理员功能）
     * @returns {Object} 按用户分组的历史订单对象
     */
    getAllUsersHistory() {
        try {
            const historyJson = localStorage.getItem(this.storageKey);
            return historyJson ? JSON.parse(historyJson) : {};
        } catch (error) {
            const logger = getLogger();
            if (logger) {
                logger.log(`获取所有用户历史订单失败: ${error.message}`, 'error');
            }
            return {};
        }
    }

    /**
     * 根据条件搜索历史订单
     * @param {Object} criteria - 搜索条件
     * @returns {Array} 匹配的订单数组
     */
    searchOrders(criteria = {}) {
        const history = this.getHistory();
        
        return history.filter(record => {
            // 按订单ID搜索
            if (criteria.orderId && !record.orderId.toLowerCase().includes(criteria.orderId.toLowerCase())) {
                return false;
            }
            
            // 按OTA参考号搜索
            if (criteria.otaReference && !record.orderData.otaReferenceNumber.toLowerCase().includes(criteria.otaReference.toLowerCase())) {
                return false;
            }
            
            // 按客户姓名搜索
            if (criteria.customerName && !record.orderData.customerName.toLowerCase().includes(criteria.customerName.toLowerCase())) {
                return false;
            }
            
            // 按客户邮箱搜索
            if (criteria.customerEmail && !record.orderData.customerEmail.toLowerCase().includes(criteria.customerEmail.toLowerCase())) {
                return false;
            }
            
            // 按日期范围搜索
            if (criteria.dateFrom) {
                const recordDate = new Date(record.timestamp);
                const fromDate = new Date(criteria.dateFrom);
                if (recordDate < fromDate) return false;
            }
            
            if (criteria.dateTo) {
                const recordDate = new Date(record.timestamp);
                const toDate = new Date(criteria.dateTo);
                toDate.setHours(23, 59, 59, 999); // 包含整天
                if (recordDate > toDate) return false;
            }
            
            return true;
        });
    }

    /**
     * 根据ID获取特定订单
     * @param {string} historyId - 历史记录ID
     * @returns {Object|null} 订单记录
     */
    getOrderById(historyId) {
        const history = this.getHistory();
        return history.find(record => record.id === historyId) || null;
    }

    /**
     * 删除历史订单
     * @param {string} historyId - 历史记录ID
     * @returns {boolean} 是否删除成功
     */
    deleteOrder(historyId) {
        try {
            const history = this.getHistory();
            const index = history.findIndex(record => record.id === historyId);
            
            if (index === -1) {
                return false;
            }
            
            history.splice(index, 1);
            localStorage.setItem(this.storageKey, JSON.stringify(history));
            
            getLogger().log(`历史订单已删除: ${historyId}`, 'info');
            return true;
            
        } catch (error) {
            getLogger().log(`删除历史订单失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 清空所有历史记录
     * @returns {boolean} 是否清空成功
     */
    clearHistory() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify([]));
            getLogger().log('所有历史订单已清空', 'info');
            return true;
        } catch (error) {
            getLogger().log(`清空历史订单失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 导出历史订单数据
     * @param {Array} orders - 要导出的订单数组（可选，默认导出所有）
     * @param {string} format - 导出格式 ('json' | 'csv')
     * @returns {string} 导出的数据字符串
     */
    exportOrders(orders = null, format = 'json') {
        const ordersToExport = orders || this.getHistory();
        
        if (format === 'csv') {
            return this.exportToCSV(ordersToExport);
        } else {
            return JSON.stringify(ordersToExport, null, 2);
        }
    }

    /**
     * 导出为CSV格式
     * @param {Array} orders - 订单数组
     * @returns {string} CSV字符串
     */
    exportToCSV(orders) {
        if (orders.length === 0) return '';
        
        // CSV头部
        const headers = [
            '订单ID', 'OTA参考号', '创建时间', '客户姓名', '客户邮箱', '客户电话',
            '上车地点', '目的地', '日期', '时间', '乘客人数', '行李数量',
            '航班信息', 'OTA渠道', '特殊要求', '创建者'
        ];
        
        // CSV数据行
        const rows = orders.map(record => [
            record.orderId,
            record.orderData.otaReferenceNumber,
            new Date(record.timestamp).toLocaleString('zh-CN'),
            record.orderData.customerName,
            record.orderData.customerEmail,
            record.orderData.customerContact,
            record.orderData.pickup,
            record.orderData.destination,
            record.orderData.date,
            record.orderData.time,
            record.orderData.passengerNumber,
            record.orderData.luggageNumber,
            record.orderData.flightInfo,
            record.orderData.otaChannel,
            record.orderData.specialRequests,
            record.metadata.createdBy
        ]);
        
        // 组合CSV
        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${(field || '').toString().replace(/"/g, '""')}"`).join(','))
            .join('\n');
            
        return csvContent;
    }

    /**
     * 生成唯一的历史记录ID
     * @returns {string} 唯一ID
     */
    generateHistoryId() {
        return 'hist_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计数据
     */
    getStatistics() {
        const history = this.getHistory();
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        return {
            total: history.length,
            today: history.filter(o => new Date(o.timestamp) >= today).length,
            week: history.filter(o => new Date(o.timestamp) >= startOfWeek).length,
            month: history.filter(o => new Date(o.timestamp) >= startOfMonth).length
        };
    }

    /**
     * 显示历史订单面板
     * @description 获取并渲染历史订单数据，然后显示面板。
     */
    showHistoryPanel() {
        const logger = getLogger(); // 获取 logger 实例
        logger.log('显示历史订单面板', 'info'); // 记录日志
        const historyPanel = document.getElementById('historyPanel'); // 获取历史面板元素
        if (historyPanel) {
            const orders = this.getHistory(); // 获取所有历史订单
            this.renderHistory(orders); // 渲染订单列表
            historyPanel.classList.remove('hidden'); // 移除 hidden 类以显示面板
            historyPanel.style.display = 'block'; // 确保显示样式
            // 不禁用body滚动，让历史面板内部可滚动
            // document.body.style.overflow = 'hidden'; // 注释掉这行，避免滚动问题
            
            // 确保面板在最前面
            historyPanel.style.zIndex = '9999';
            
            logger.log('历史订单面板已显示', 'success');
        } else {
            logger.log('历史订单面板元素未找到', 'error');
        }
    }

    /**
     * 隐藏历史订单面板
     * @description 关闭并隐藏历史订单面板。
     */
    hideHistoryPanel() {
        const logger = getLogger(); // 获取 logger 实例
        logger.log('隐藏历史订单面板', 'info'); // 记录日志
        const historyPanel = document.getElementById('historyPanel'); // 获取历史面板元素
        if (historyPanel) {
            historyPanel.classList.add('hidden'); // 添加 hidden 类以隐藏面板
            historyPanel.style.display = 'none'; // 确保隐藏样式
            document.body.style.overflow = ''; // 恢复背景滚动
            
            logger.log('历史订单面板已隐藏', 'success');
        }
    }

    /**
     * 渲染历史订单列表
     * @param {Array<object>} orders - 要渲染的订单数组。
     */
    renderHistory(orders) {
        const container = document.getElementById('historyListContainer'); // 获取列表容器
        const logger = getLogger(); // 获取 logger 实例

        if (!container) {
            logger.log('历史订单列表容器未找到', 'error'); // 如果容器不存在，记录错误
            return;
        }

        if (orders.length === 0) {
            // 如果没有订单，显示空状态
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📝</div>
                    <div class="empty-text">暂无历史订单</div>
                </div>
            `;
        } else {
            // 如果有订单，则生成并插入订单项HTML
            container.innerHTML = orders.map(order => {
                // 判断订单状态
                const isSuccess = order.metadata?.apiResponse?.success !== false && 
                                !order.orderId.startsWith('FAILED_') && 
                                !order.orderId.startsWith('EXCEPTION_');
                const statusClass = isSuccess ? 'success' : 'failed';
                const statusText = isSuccess ? '✅ 成功' : '❌ 失败';
                const statusColor = isSuccess ? '#28a745' : '#dc3545';
                
                return `
                <div class="history-item ${statusClass}" data-id="${order.id}">
                    <div class="history-item-header">
                        <span class="history-item-id">订单ID: ${order.orderId || 'N/A'}</span>
                        <span class="history-item-status" style="color: ${statusColor}; font-weight: bold;">${statusText}</span>
                        <span class="history-item-time">${new Date(order.timestamp).toLocaleString()}</span>
                    </div>
                    <div class="history-item-content">
                        <p><strong>客户:</strong> ${order.orderData.customerName || 'N/A'}</p>
                        <p><strong>服务:</strong> ${order.orderData.subCategoryId || 'N/A'}</p>
                        <p><strong>OTA参考号:</strong> ${order.orderData.otaReferenceNumber || 'N/A'}</p>
                        ${!isSuccess ? `<p><strong>错误信息:</strong> <span style="color: #dc3545;">${order.metadata?.apiResponse?.message || '未知错误'}</span></p>` : ''}
                    </div>
                </div>
                `;
            }).join('');
        }
    }

    /**
     * 初始化历史订单面板的事件监听器
     * @description 为关闭、清空、导出和搜索按钮绑定事件。
     */
    initEventListeners() {
        document.getElementById('closeHistoryBtn')?.addEventListener('click', () => this.hideHistoryPanel()); // 关闭按钮
        document.getElementById('clearHistoryBtn')?.addEventListener('click', () => {
            if (confirm('确定要清空所有历史订单吗？此操作不可恢复。')) {
                this.clearHistory(); // 清空历史
                this.renderHistory([]); // 重新渲染为空列表
            }
        });
        document.getElementById('exportHistoryBtn')?.addEventListener('click', () => this.exportOrders()); // 导出按钮
        document.getElementById('searchHistoryBtn')?.addEventListener('click', () => {
            const criteria = {
                orderId: document.getElementById('searchOrderId').value,
                customer: document.getElementById('searchCustomer').value,
                dateFrom: document.getElementById('searchDateFrom').value,
                dateTo: document.getElementById('searchDateTo').value,
            };
            const results = this.searchOrders(criteria); // 执行搜索
            this.renderHistory(results); // 渲染搜索结果
        });
        document.getElementById('resetSearchBtn')?.addEventListener('click', () => {
            document.getElementById('searchOrderId').value = '';
            document.getElementById('searchCustomer').value = '';
            document.getElementById('searchDateFrom').value = '';
            document.getElementById('searchDateTo').value = '';
            this.renderHistory(this.getHistory()); // 重置并渲染所有历史
        });
    }
}

// 创建全局实例
let orderHistoryManagerInstance = null;

/**
 * 获取历史订单管理器实例
 * @returns {OrderHistoryManager} 管理器实例
 */
function getOrderHistoryManager() {
    if (!orderHistoryManagerInstance) {
        orderHistoryManagerInstance = new OrderHistoryManager();
    }
    return orderHistoryManagerInstance;
}

// 导出到全局作用域
window.OrderHistoryManager = OrderHistoryManager;
window.getOrderHistoryManager = getOrderHistoryManager;
