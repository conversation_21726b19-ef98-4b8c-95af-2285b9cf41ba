<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>快速方法修复验证</title>
</head>
<body>
    <h1>快速方法修复验证</h1>
    <div id="results"></div>

    <!-- 加载必要的脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/multi-order-manager.js"></script>

    <script>
        function runQuickTest() {
            const results = document.getElementById('results');
            let output = '<h2>测试结果:</h2>';
            
            try {
                // 1. 检查类是否存在
                output += `<p>✅ MultiOrderManager类存在: ${!!window.MultiOrderManager}</p>`;
                
                // 2. 检查实例是否存在
                const manager = window.OTA?.multiOrderManager || window.multiOrderManager;
                output += `<p>✅ 管理器实例存在: ${!!manager}</p>`;
                
                if (manager) {
                    // 3. 检查关键方法
                    const methods = ['handleMultiOrderDetection', 'handleOrderStateChange'];
                    methods.forEach(method => {
                        const exists = typeof manager[method] === 'function';
                        output += `<p>${exists ? '✅' : '❌'} ${method}: ${exists ? '存在' : '不存在'}</p>`;
                    });
                    
                    // 4. 测试方法调用
                    try {
                        manager.handleMultiOrderDetection([], '测试文本');
                        output += `<p>✅ handleMultiOrderDetection 调用成功</p>`;
                    } catch (error) {
                        output += `<p>❌ handleMultiOrderDetection 调用失败: ${error.message}</p>`;
                    }
                    
                    try {
                        manager.handleOrderStateChange({test: 'data'});
                        output += `<p>✅ handleOrderStateChange 调用成功</p>`;
                    } catch (error) {
                        output += `<p>❌ handleOrderStateChange 调用失败: ${error.message}</p>`;
                    }
                    
                    // 5. 测试事件分发
                    try {
                        const event = new CustomEvent('multiOrderDetected', {
                            detail: {
                                data: [{test: 'order1'}, {test: 'order2'}],
                                orderText: '测试多订单文本'
                            }
                        });
                        document.dispatchEvent(event);
                        output += `<p>✅ 多订单检测事件分发成功</p>`;
                    } catch (error) {
                        output += `<p>❌ 多订单检测事件分发失败: ${error.message}</p>`;
                    }
                }
                
            } catch (error) {
                output += `<p>❌ 测试过程出错: ${error.message}</p>`;
            }
            
            results.innerHTML = output;
        }
        
        // 等待脚本加载后运行测试
        window.addEventListener('load', () => {
            setTimeout(runQuickTest, 1000);
        });
        
        // 监听错误
        window.addEventListener('error', (event) => {
            console.error('页面错误:', event.error);
            const results = document.getElementById('results');
            if (results) {
                results.innerHTML += `<p style="color: red;">❌ 页面错误: ${event.error.message}</p>`;
            }
        });
    </script>
</body>
</html>
