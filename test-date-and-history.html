<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期和历史记录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
        #output {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧪 日期解析和历史记录测试</h1>
    
    <div class="test-panel">
        <h2>测试项目</h2>
        <button class="btn" onclick="testCurrentDate()">1. 测试当前日期传递</button>
        <button class="btn" onclick="testGeminiDateParsing()">2. 测试Gemini日期解析</button>
        <button class="btn" onclick="testHistoryManager()">3. 测试历史记录功能</button>
        <button class="btn" onclick="simulateOrderCreation()">4. 模拟订单创建</button>
        <button class="btn" onclick="clearOutput()">清除输出</button>
    </div>

    <div class="test-panel">
        <h2>测试输出</h2>
        <div id="output">等待测试...</div>
    </div>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                'info': '#17a2b8',
                'success': '#28a745',
                'error': '#dc3545',
                'warning': '#ffc107'
            };
            
            output.innerHTML += `<span style="color: ${colors[type] || '#333'};">[${timestamp}] ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
        }

        function clearOutput() {
            document.getElementById('output').innerHTML = '';
        }

        function testCurrentDate() {
            log('🗓️ 测试当前日期传递功能', 'info');
            
            const currentDate = new Date();
            const dateContext = `
当前日期信息（用于相对日期计算）:
- 当前日期: ${currentDate.toLocaleDateString('zh-CN')} (${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')})
- 当前年份: ${currentDate.getFullYear()}
- 当前月份: ${currentDate.getMonth() + 1}
- 今天是: ${currentDate.toLocaleDateString('zh-CN', { weekday: 'long' })}
`;
            
            log('✅ 日期上下文信息：', 'success');
            log(dateContext, 'info');
            log('这些信息现在会自动传递给Gemini以确保年份正确', 'success');
        }

        function testGeminiDateParsing() {
            log('🤖 测试Gemini日期解析功能', 'info');
            
            // 检查Gemini服务是否可用
            if (typeof window.getGeminiService === 'function') {
                const geminiService = window.getGeminiService();
                if (geminiService) {
                    log('✅ Gemini服务已加载', 'success');
                    log('现在Gemini会接收当前日期信息来正确解析相对日期', 'info');
                    
                    // 模拟一些相对日期测试
                    const testDates = ['明天', '后天', '下周一', '下个月1号'];
                    testDates.forEach(date => {
                        log(`📅 测试日期: "${date}" - 现在会基于${new Date().getFullYear()}年正确解析`, 'info');
                    });
                } else {
                    log('❌ Gemini服务未初始化', 'error');
                }
            } else {
                log('❌ Gemini服务未加载', 'error');
            }
        }

        function testHistoryManager() {
            log('📚 测试历史记录管理器', 'info');
            
            // 检查历史管理器是否可用
            if (typeof window.getOrderHistoryManager === 'function') {
                const historyManager = window.getOrderHistoryManager();
                if (historyManager) {
                    log('✅ 历史记录管理器已加载', 'success');
                    
                    // 测试添加成功记录
                    const testOrderData = {
                        customerName: '测试客户',
                        otaReferenceNumber: 'TEST001',
                        subCategoryId: '2'
                    };
                    
                    try {
                        // 添加成功订单
                        historyManager.addOrder(testOrderData, 'SUCCESS_TEST_001', {
                            success: true,
                            message: '测试成功订单'
                        });
                        log('✅ 成功订单记录已添加', 'success');
                        
                        // 添加失败订单
                        historyManager.addOrder(testOrderData, 'FAILED_TEST_001', {
                            success: false,
                            message: '测试失败订单'
                        });
                        log('✅ 失败订单记录已添加', 'success');
                        
                        // 获取历史记录数量
                        const history = historyManager.getHistory();
                        log(`📊 当前历史记录数量: ${history.length}`, 'info');
                        
                    } catch (error) {
                        log(`❌ 历史记录测试失败: ${error.message}`, 'error');
                    }
                } else {
                    log('❌ 历史记录管理器未初始化', 'error');
                }
            } else {
                log('❌ 历史记录管理器未加载', 'error');
            }
        }

        function simulateOrderCreation() {
            log('🎭 模拟订单创建流程', 'info');
            
            // 检查事件管理器
            const eventManager = window.OTA?.uiManager?.managers?.event;
            if (eventManager && typeof eventManager.getOrderHistoryManager === 'function') {
                log('✅ 事件管理器包含历史记录功能', 'success');
                
                const historyManager = eventManager.getOrderHistoryManager();
                if (historyManager) {
                    log('✅ 事件管理器可以获取历史管理器', 'success');
                    log('现在订单创建成功/失败都会被记录到历史中', 'info');
                } else {
                    log('❌ 事件管理器无法获取历史管理器', 'error');
                }
            } else {
                log('❌ 事件管理器未加载或缺少历史记录功能', 'error');
            }
            
            log('💡 提示: 现在当您创建订单时，无论成功还是失败都会自动记录到历史中', 'warning');
        }

        // 页面加载完成后的初始化检查
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('🚀 日期和历史记录测试工具已加载', 'success');
                log('请点击上方按钮进行各项测试', 'info');
            }, 1000);
        });
    </script>
    
    <!-- 引入必要的脚本文件 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/order-history-manager.js"></script>
    <script src="js/managers/state-manager.js"></script>
    <script src="js/managers/form-manager.js"></script>
    <script src="js/managers/event-manager.js"></script>
    <script src="js/ui-manager.js"></script>
</body>
</html>
