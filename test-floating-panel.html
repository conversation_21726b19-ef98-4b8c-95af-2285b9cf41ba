<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单浮窗测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        
        .test-btn:hover {
            background: #0056b3;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔢 多订单浮窗测试</h1>
        
        <div class="status">
            <strong>测试说明：</strong>
            <p>这个页面用于测试多订单浮窗的显示效果。点击下面的按钮来查看浮窗是否按预期的样式显示。</p>
        </div>
        
        <div style="text-align: center;">
            <button class="test-btn" onclick="testFloatingPanel()">📱 显示多订单浮窗</button>
            <button class="test-btn" onclick="testPanelFeatures()">🧪 测试浮窗功能</button>
            <button class="test-btn" onclick="hidePanel()">❌ 关闭浮窗</button>
        </div>
        
        <div id="testResults" style="margin-top: 20px;"></div>
        
        <div style="margin-top: 30px;">
            <h3>✅ 预期效果：</h3>
            <ul>
                <li>浮窗应该显示在页面中央</li>
                <li>浮窗背景应该有毛玻璃效果</li>
                <li>浮窗大小约为视窗的90%宽度，80%高度</li>
                <li>浮窗应该可以拖拽移动</li>
                <li>浮窗应该有最小化/最大化按钮</li>
                <li>浮窗内容应该显示3个解析好的订单</li>
            </ul>
        </div>
    </div>

    <!-- 多订单面板 HTML 结构 -->
    <div id="multiOrderPanel" class="multi-order-panel hidden">
        <div class="multi-order-content">
            <div class="multi-order-header">
                <h3>🔢 多订单预览与编辑</h3>
                <div class="multi-order-controls">
                    <div class="order-stats">
                        <span id="multiOrderCount" class="order-count">3 个订单</span>
                        <span id="multiOrderDateRange" class="date-range">2024-01-15 至 2024-01-17</span>
                    </div>
                    <div class="header-actions">
                        <button type="button" id="batchCreateBtn" class="btn btn-primary btn-sm">批量创建</button>
                        <button type="button" id="closeMultiOrderBtn" class="btn btn-icon" title="关闭">✕</button>
                    </div>
                </div>
            </div>

            <div class="multi-order-list" id="multiOrderList">
                <div class="order-item" data-order-index="0">
                    <div class="order-header">
                        <div class="order-selector">
                            <input type="checkbox" id="order-0" checked />
                            <label for="order-0">订单 1</label>
                        </div>
                        <div class="order-status">
                            <span class="status-badge status-parsed">已解析</span>
                        </div>
                    </div>
                    <div class="order-content">
                        <div class="order-summary">
                            <div class="order-summary-grid">
                                <div class="summary-item">
                                    <label>客户:</label>
                                    <span>张三</span>
                                </div>
                                <div class="summary-item">
                                    <label>路线:</label>
                                    <span>吉隆坡国际机场 → 阳光广场酒店</span>
                                </div>
                                <div class="summary-item">
                                    <label>时间:</label>
                                    <span>2024-01-15 10:30</span>
                                </div>
                                <div class="summary-item">
                                    <label>乘客:</label>
                                    <span>2人</span>
                                </div>
                                <div class="summary-item">
                                    <label>价格:</label>
                                    <span>MYR 120.00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="order-actions">
                        <button type="button" class="btn btn-sm btn-outline">📋 详情</button>
                        <button type="button" class="btn btn-sm btn-outline">✏️ 编辑</button>
                        <button type="button" class="btn btn-sm btn-primary">🤖 解析</button>
                    </div>
                </div>
                
                <div class="order-item" data-order-index="1">
                    <div class="order-header">
                        <div class="order-selector">
                            <input type="checkbox" id="order-1" checked />
                            <label for="order-1">订单 2</label>
                        </div>
                        <div class="order-status">
                            <span class="status-badge status-parsed">已解析</span>
                        </div>
                    </div>
                    <div class="order-content">
                        <div class="order-summary">
                            <div class="order-summary-grid">
                                <div class="summary-item">
                                    <label>客户:</label>
                                    <span>李四</span>
                                </div>
                                <div class="summary-item">
                                    <label>路线:</label>
                                    <span>双威酒店 → 吉隆坡国际机场</span>
                                </div>
                                <div class="summary-item">
                                    <label>时间:</label>
                                    <span>2024-01-16 08:00</span>
                                </div>
                                <div class="summary-item">
                                    <label>乘客:</label>
                                    <span>3人</span>
                                </div>
                                <div class="summary-item">
                                    <label>价格:</label>
                                    <span>MYR 150.00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="order-actions">
                        <button type="button" class="btn btn-sm btn-outline">📋 详情</button>
                        <button type="button" class="btn btn-sm btn-outline">✏️ 编辑</button>
                        <button type="button" class="btn btn-sm btn-primary">🤖 解析</button>
                    </div>
                </div>
                
                <div class="order-item" data-order-index="2">
                    <div class="order-header">
                        <div class="order-selector">
                            <input type="checkbox" id="order-2" checked />
                            <label for="order-2">订单 3</label>
                        </div>
                        <div class="order-status">
                            <span class="status-badge status-parsed">已解析</span>
                        </div>
                    </div>
                    <div class="order-content">
                        <div class="order-summary">
                            <div class="order-summary-grid">
                                <div class="summary-item">
                                    <label>客户:</label>
                                    <span>王五</span>
                                </div>
                                <div class="summary-item">
                                    <label>路线:</label>
                                    <span>云顶高原 → 马六甲古城</span>
                                </div>
                                <div class="summary-item">
                                    <label>时间:</label>
                                    <span>2024-01-17 09:00</span>
                                </div>
                                <div class="summary-item">
                                    <label>乘客:</label>
                                    <span>4人</span>
                                </div>
                                <div class="summary-item">
                                    <label>价格:</label>
                                    <span>MYR 200.00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="order-actions">
                        <button type="button" class="btn btn-sm btn-outline">📋 详情</button>
                        <button type="button" class="btn btn-sm btn-outline">✏️ 编辑</button>
                        <button type="button" class="btn btn-sm btn-primary">🤖 解析</button>
                    </div>
                </div>
            </div>

            <div class="multi-order-footer">
                <div class="batch-actions">
                    <button type="button" class="btn btn-outline btn-sm">全选</button>
                    <button type="button" class="btn btn-outline btn-sm">取消全选</button>
                    <button type="button" class="btn btn-outline btn-sm">验证全部</button>
                </div>
                <div class="creation-summary">
                    <span>已选择 3 个订单</span>
                    <button type="button" class="btn btn-primary">创建选中订单</button>
                </div>
            </div>
        </div>
    </div>

    <link rel="stylesheet" href="style.css">
    
    <script>
        function testFloatingPanel() {
            const panel = document.getElementById('multiOrderPanel');
            const results = document.getElementById('testResults');
            
            if (!panel) {
                results.innerHTML = '<div style="color: red;">❌ 未找到面板元素</div>';
                return;
            }
            
            // 显示面板
            panel.classList.remove('hidden');
            panel.style.display = 'flex';
            
            // 检查样式
            setTimeout(() => {
                const rect = panel.getBoundingClientRect();
                const style = window.getComputedStyle(panel);
                
                const checks = {
                    position: style.position === 'fixed' ? '✅' : '❌',
                    centered: (Math.abs(rect.left + rect.width/2 - window.innerWidth/2) < 100) ? '✅' : '❌',
                    zIndex: parseInt(style.zIndex) >= 2000 ? '✅' : '❌',
                    backdrop: style.backdropFilter.includes('blur') ? '✅' : '❌',
                    animation: style.animation.includes('multiOrderPanelShow') ? '✅' : '❌'
                };
                
                results.innerHTML = `
                    <div class="status">
                        <h4>📊 浮窗状态检查：</h4>
                        <p>${checks.position} 固定定位 (position: ${style.position})</p>
                        <p>${checks.centered} 居中显示 (偏差: ${Math.abs(rect.left + rect.width/2 - window.innerWidth/2).toFixed(1)}px)</p>
                        <p>${checks.zIndex} 层级正确 (z-index: ${style.zIndex})</p>
                        <p>${checks.backdrop} 毛玻璃效果 (backdrop-filter: ${style.backdropFilter})</p>
                        <p>${checks.animation} 显示动画 (animation: ${style.animation})</p>
                        <p>📐 面板尺寸: ${rect.width.toFixed(0)} × ${rect.height.toFixed(0)} px</p>
                        <p>🎯 面板位置: (${rect.left.toFixed(0)}, ${rect.top.toFixed(0)})</p>
                    </div>
                `;
            }, 100);
        }
        
        function testPanelFeatures() {
            const panel = document.getElementById('multiOrderPanel');
            const results = document.getElementById('testResults');
            
            if (!panel || panel.classList.contains('hidden')) {
                results.innerHTML = '<div style="color: orange;">⚠️ 请先显示面板</div>';
                return;
            }
            
            const features = {
                dragHandle: panel.querySelector('.multi-order-header') ? '✅' : '❌',
                closeButton: panel.querySelector('#closeMultiOrderBtn') ? '✅' : '❌',
                orderItems: panel.querySelectorAll('.order-item').length,
                actionButtons: panel.querySelectorAll('.order-actions button').length,
                checkboxes: panel.querySelectorAll('input[type="checkbox"]').length
            };
            
            results.innerHTML = `
                <div class="status">
                    <h4>🧪 功能特性检查：</h4>
                    <p>${features.dragHandle} 拖拽手柄 (标题栏)</p>
                    <p>${features.closeButton} 关闭按钮</p>
                    <p>📋 订单项数量: ${features.orderItems}</p>
                    <p>🔘 操作按钮数量: ${features.actionButtons}</p>
                    <p>☑️ 选择框数量: ${features.checkboxes}</p>
                </div>
            `;
        }
        
        function hidePanel() {
            const panel = document.getElementById('multiOrderPanel');
            const results = document.getElementById('testResults');
            
            if (panel) {
                panel.classList.add('hidden');
                panel.style.display = 'none';
                results.innerHTML = '<div class="status">🚪 浮窗已关闭</div>';
            }
        }
        
        // 添加关闭按钮事件
        document.getElementById('closeMultiOrderBtn').addEventListener('click', hidePanel);
        
        // 添加拖拽功能测试
        let isDragging = false;
        let currentX = 0;
        let currentY = 0;
        let initialX = 0;
        let initialY = 0;
        
        const header = document.querySelector('.multi-order-header');
        const panel = document.getElementById('multiOrderPanel');
        
        if (header && panel) {
            header.style.cursor = 'move';
            header.title = '拖拽移动面板';
            
            header.addEventListener('mousedown', (e) => {
                if (e.target.closest('button')) return;
                
                initialX = e.clientX - currentX;
                initialY = e.clientY - currentY;
                isDragging = true;
                panel.style.transition = 'none';
            });
            
            document.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    e.preventDefault();
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;
                    
                    panel.style.transform = `translate(calc(-50% + ${currentX}px), calc(-50% + ${currentY}px))`;
                }
            });
            
            document.addEventListener('mouseup', () => {
                isDragging = false;
                panel.style.transition = 'all 0.25s ease-in-out';
            });
        }
    </script>
</body>
</html>
