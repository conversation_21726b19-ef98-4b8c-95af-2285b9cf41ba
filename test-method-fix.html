<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单方法修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #b8daff; }
        
        textarea {
            width: 100%;
            height: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
        }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        .log-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .method-test {
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 多订单方法修复测试</h1>
        <p><strong>目标:</strong> 验证 handleMultiOrderDetection 和 handleOrderStateChange 方法是否正确修复</p>
        
        <div class="test-section">
            <h3>📊 系统初始化状态</h3>
            <div id="initStatus" class="status info">正在检查系统状态...</div>
            <button onclick="checkSystemStatus()">重新检查系统状态</button>
        </div>

        <div class="test-section">
            <h3>🧪 方法存在性测试</h3>
            <div id="methodTestResults"></div>
            <button onclick="testMethods()">测试方法是否存在</button>
        </div>

        <div class="test-section">
            <h3>🚀 多订单检测事件测试</h3>
            <textarea id="testOrderInput" placeholder="输入多订单文本进行测试...">
订单1:
客户: 张三 
电话: 13800138001
从: KLIA机场 到: 吉隆坡市中心
日期: 2024-12-20 时间: 10:00

订单2:
客户: 李四
电话: 13800138002  
从: 吉隆坡市中心 到: KLIA2机场
日期: 2024-12-21 时间: 15:30
            </textarea>
            <button onclick="testMultiOrderDetection()">测试多订单检测</button>
            <button onclick="testOrderStateChange()">测试订单状态变化</button>
            <button onclick="simulateRealtimeAnalysis()">模拟实时分析流程</button>
        </div>

        <div class="test-section">
            <h3>📝 测试日志输出</h3>
            <div id="logOutput" class="log-output">等待测试开始...</div>
            <button onclick="clearLog()">清除日志</button>
        </div>
    </div>

    <!-- 必须的系统脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/multi-order-manager.js"></script>
    <script src="js/managers/realtime-analysis-manager.js"></script>

    <script>
        // 测试脚本
        let testLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            testLog.push(logMessage);
            
            const logOutput = document.getElementById('logOutput');
            logOutput.textContent = testLog.join('\n');
            logOutput.scrollTop = logOutput.scrollHeight;
            
            console.log(logMessage);
        }
        
        function clearLog() {
            testLog = [];
            document.getElementById('logOutput').textContent = '日志已清除...';
        }
        
        function checkSystemStatus() {
            const statusEl = document.getElementById('initStatus');
            
            try {
                // 检查必要的模块
                const checks = {
                    'MultiOrderManager类': !!window.MultiOrderManager,
                    'multiOrderManager实例': !!window.OTA?.multiOrderManager,
                    'Logger模块': !!window.OTA?.logger,
                    'AppState模块': !!window.OTA?.appState,
                    'RealtimeAnalysisManager': !!window.OTA?.managers?.RealtimeAnalysisManager
                };
                
                const allPassed = Object.values(checks).every(Boolean);
                
                if (allPassed) {
                    statusEl.className = 'status success';
                    statusEl.textContent = '✅ 所有系统模块已加载';
                } else {
                    statusEl.className = 'status error';
                    statusEl.textContent = '❌ 部分系统模块未加载: ' + 
                        Object.entries(checks)
                            .filter(([key, value]) => !value)
                            .map(([key]) => key)
                            .join(', ');
                }
                
                log('系统状态检查完成: ' + JSON.stringify(checks, null, 2));
                
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 系统状态检查失败: ' + error.message;
                log('系统状态检查错误: ' + error.message, 'error');
            }
        }
        
        function testMethods() {
            const resultsEl = document.getElementById('methodTestResults');
            
            try {
                const manager = window.OTA?.multiOrderManager || window.multiOrderManager;
                
                if (!manager) {
                    resultsEl.innerHTML = '<div class="method-test"><strong>❌ 多订单管理器未找到</strong></div>';
                    log('多订单管理器未找到', 'error');
                    return;
                }
                
                const methods = [
                    'handleMultiOrderDetection',
                    'handleOrderStateChange', 
                    'setupEventListeners',
                    'analyzeInputForMultiOrder',
                    'showMultiOrderPanel'
                ];
                
                const results = methods.map(methodName => {
                    const exists = typeof manager[methodName] === 'function';
                    return {
                        name: methodName,
                        exists,
                        type: typeof manager[methodName]
                    };
                });
                
                const allExist = results.every(r => r.exists);
                
                resultsEl.innerHTML = `
                    <div class="method-test">
                        <strong>${allExist ? '✅' : '❌'} 方法存在性测试结果:</strong><br>
                        ${results.map(r => 
                            `${r.exists ? '✅' : '❌'} ${r.name}: ${r.type}`
                        ).join('<br>')}
                    </div>
                `;
                
                log('方法测试完成: ' + JSON.stringify(results, null, 2));
                
            } catch (error) {
                resultsEl.innerHTML = `<div class="method-test"><strong>❌ 方法测试失败: ${error.message}</strong></div>`;
                log('方法测试错误: ' + error.message, 'error');
            }
        }
        
        function testMultiOrderDetection() {
            try {
                const testData = [
                    { customerName: '张三', pickup: 'KLIA', dropoff: '市中心' },
                    { customerName: '李四', pickup: '市中心', dropoff: 'KLIA2' }
                ];
                const orderText = document.getElementById('testOrderInput').value;
                
                log('开始测试多订单检测事件...');
                
                // 触发事件
                const event = new CustomEvent('multiOrderDetected', {
                    detail: {
                        data: testData,
                        orderText: orderText
                    }
                });
                
                document.dispatchEvent(event);
                log('✅ 多订单检测事件已触发', 'success');
                
            } catch (error) {
                log('❌ 多订单检测测试失败: ' + error.message, 'error');
            }
        }
        
        function testOrderStateChange() {
            try {
                const testOrderData = {
                    customerName: '测试客户',
                    pickup: '测试起点',
                    dropoff: '测试终点',
                    status: 'processed'
                };
                
                log('开始测试订单状态变化事件...');
                
                // 触发事件
                const event = new CustomEvent('appStateChanged', {
                    detail: {
                        key: 'currentOrder',
                        value: testOrderData
                    }
                });
                
                document.dispatchEvent(event);
                log('✅ 订单状态变化事件已触发', 'success');
                
            } catch (error) {
                log('❌ 订单状态变化测试失败: ' + error.message, 'error');
            }
        }
        
        function simulateRealtimeAnalysis() {
            try {
                const orderText = document.getElementById('testOrderInput').value;
                log('开始模拟实时分析流程...');
                
                // 模拟实时分析管理器的流程
                setTimeout(() => {
                    const mockResult = {
                        data: [
                            { customerName: '张三', pickup: 'KLIA', dropoff: '市中心' },
                            { customerName: '李四', pickup: '市中心', dropoff: 'KLIA2' }
                        ]
                    };
                    
                    const event = new CustomEvent('multiOrderDetected', {
                        detail: {
                            data: mockResult.data,
                            orderText: orderText
                        }
                    });
                    
                    document.dispatchEvent(event);
                    log('✅ 模拟实时分析流程完成', 'success');
                }, 1000);
                
                log('⏳ 模拟分析中...');
                
            } catch (error) {
                log('❌ 模拟实时分析失败: ' + error.message, 'error');
            }
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', () => {
            setTimeout(checkSystemStatus, 500);
        });
        
        // 监听多订单检测事件
        document.addEventListener('multiOrderDetected', (event) => {
            log('📡 收到多订单检测事件: ' + JSON.stringify(event.detail, null, 2));
        });
        
        // 监听应用状态变化事件
        document.addEventListener('appStateChanged', (event) => {
            log('📡 收到应用状态变化事件: ' + JSON.stringify(event.detail, null, 2));
        });
        
        // 全局错误处理
        window.addEventListener('error', (event) => {
            log('❌ 全局错误: ' + event.error.message, 'error');
        });
    </script>
</body>
</html>
