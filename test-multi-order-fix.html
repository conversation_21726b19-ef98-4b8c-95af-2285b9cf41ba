<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单模组测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .test-container { max-width: 1200px; margin: 20px auto; padding: 20px; }
        .test-section { border: 1px solid #ddd; margin: 20px 0; padding: 20px; background: #f9f9f9; }
        .test-input { width: 100%; height: 150px; margin: 10px 0; }
        .test-button { margin: 10px 5px; padding: 10px 20px; }
        .log-output { background: #000; color: #00ff00; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 多订单模组功能测试</h1>
        
        <div class="test-section">
            <h3>📝 测试输入</h3>
            <textarea id="orderInput" class="test-input" placeholder="请输入多订单内容...">客户：张三
电话：13800138001
上车：首都机场T3
下车：三里屯
日期：2025-01-15
时间：14:00

客户：李四  
电话：13800138002
上车：北京站
下车：中关村
日期：2025-01-16
时间：10:30</textarea>
            
            <button id="testBtn" class="test-button btn btn-primary">🚀 测试多订单检测</button>
            <button id="clearBtn" class="test-button btn btn-outline">🧹 清空日志</button>
            <button id="statusBtn" class="test-button btn btn-success">📊 检查状态</button>
        </div>

        <div class="test-section">
            <h3>📋 实时日志</h3>
            <div id="logOutput" class="log-output">等待测试...</div>
        </div>
    </div>

    <!-- 多订单面板 -->
    <div id="multiOrderPanel" class="multi-order-panel hidden">
        <div class="multi-order-overlay">
            <div class="multi-order-content">
                <div class="multi-order-header">
                    <h3>🔢 多订单预览与编辑</h3>
                    <div class="multi-order-controls">
                        <div class="order-stats">
                            <span id="multiOrderCount" class="order-count">0 个订单</span>
                            <span id="multiOrderDateRange" class="date-range"></span>
                        </div>
                        <div class="header-actions">
                            <button type="button" id="closeMultiOrderBtn" class="btn btn-icon">✕</button>
                        </div>
                    </div>
                </div>

                <div class="multi-order-list" id="multiOrderList">
                    <!-- 多订单项将在这里动态生成 -->
                </div>

                <div class="multi-order-footer">
                    <div class="creation-summary">
                        <span id="selectedOrderCount">已选择 0 个订单</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载核心脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/monitoring-wrapper.js"></script>
    <script src="js/ota-channel-mapping.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/multi-order-manager.js"></script>
    <script src="js/ui-manager.js"></script>

    <script>
        let logMessages = [];

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logOutput = document.getElementById('logOutput');
            logMessages.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            logOutput.innerHTML = logMessages.join('\n');
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(`[TEST] ${message}`);
        }

        function clearLog() {
            logMessages = [];
            document.getElementById('logOutput').innerHTML = '日志已清空...';
        }

        function checkStatus() {
            addLog('=== 系统状态检查 ===');
            addLog(`OTA命名空间: ${!!window.OTA ? '✅ 存在' : '❌ 不存在'}`);
            addLog(`Logger: ${!!window.OTA?.logger ? '✅ 存在' : '❌ 不存在'}`);
            addLog(`AppState: ${!!window.OTA?.appState ? '✅ 存在' : '❌ 不存在'}`);
            addLog(`GeminiService: ${!!window.OTA?.geminiService ? '✅ 存在' : '❌ 不存在'}`);
            addLog(`MultiOrderManager类: ${!!window.MultiOrderManager ? '✅ 存在' : '❌ 不存在'}`);
            addLog(`getMultiOrderManager函数: ${!!window.getMultiOrderManager ? '✅ 存在' : '❌ 不存在'}`);
            addLog(`多订单管理器实例: ${!!window.OTA?.multiOrderManager ? '✅ 存在' : '❌ 不存在'}`);
            addLog(`orderInput元素: ${!!document.getElementById('orderInput') ? '✅ 存在' : '❌ 不存在'}`);
            addLog(`multiOrderPanel元素: ${!!document.getElementById('multiOrderPanel') ? '✅ 存在' : '❌ 不存在'}`);
            addLog('=== 状态检查完成 ===');
        }

        async function testMultiOrder() {
            addLog('=== 开始多订单测试 ===');
            
            const orderInput = document.getElementById('orderInput');
            const text = orderInput.value.trim();
            
            if (!text) {
                addLog('❌ 请输入测试内容', 'error');
                return;
            }

            addLog(`📝 输入文本长度: ${text.length}字符`);

            try {
                // 检查多订单管理器
                const manager = window.OTA?.multiOrderManager || window.getMultiOrderManager?.();
                if (!manager) {
                    throw new Error('多订单管理器不存在');
                }
                addLog('✅ 多订单管理器获取成功');

                // 检查Gemini服务
                const geminiService = window.OTA?.geminiService;
                if (!geminiService) {
                    throw new Error('Gemini服务不存在');
                }
                addLog('✅ Gemini服务获取成功');

                // 手动调用分析方法
                addLog('🔍 开始调用analyzeInputForMultiOrder...');
                await manager.analyzeInputForMultiOrder(text);
                addLog('✅ analyzeInputForMultiOrder调用完成');

                // 检查面板状态
                const panel = document.getElementById('multiOrderPanel');
                const isVisible = panel && !panel.classList.contains('hidden');
                addLog(`👁️ 多订单面板状态: ${isVisible ? '✅ 已显示' : '❌ 未显示'}`);

            } catch (error) {
                addLog(`❌ 测试失败: ${error.message}`, 'error');
                console.error('Test error:', error);
            }
            
            addLog('=== 多订单测试完成 ===');
        }

        // 页面加载后初始化
        window.addEventListener('load', () => {
            addLog('🚀 页面加载完成，初始化系统...');
            
            // 等待模块加载
            setTimeout(() => {
                // 初始化多订单管理器
                try {
                    const manager = window.getMultiOrderManager?.();
                    if (manager && typeof manager.init === 'function') {
                        manager.init();
                        addLog('✅ 多订单管理器初始化完成');
                    } else {
                        addLog('⚠️ 多订单管理器初始化跳过（方法不存在）', 'warning');
                    }
                } catch (error) {
                    addLog(`❌ 多订单管理器初始化失败: ${error.message}`, 'error');
                }

                checkStatus();
            }, 1000);
        });

        // 绑定按钮事件
        document.getElementById('testBtn').addEventListener('click', testMultiOrder);
        document.getElementById('clearBtn').addEventListener('click', clearLog);
        document.getElementById('statusBtn').addEventListener('click', checkStatus);

        // 关闭面板按钮
        document.getElementById('closeMultiOrderBtn').addEventListener('click', () => {
            document.getElementById('multiOrderPanel').classList.add('hidden');
            addLog('🔒 多订单面板已关闭');
        });
    </script>
</body>
</html>
